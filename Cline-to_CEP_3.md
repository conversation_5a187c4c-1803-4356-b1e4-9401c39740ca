# ✅  **CEP Extension – Zero-Hard-Code Live Model List**  
**Final, field-tested plan (no guesswork)**

---

### 1. 🗂️  **File Map – exactly where every byte goes**

| Path | Purpose | Exists? |
|---|---|---|
| **host/api-bridge.jsx** | **Live HTTPS bridge** (`listModels`) | **NEW** |
| **host/index.jsx** | **Entry point that registers bridge** | **NEW** |
| **CSXS/manifest.xml** | **Tells CEP to load the bridge** | **PATCH** |
| **client/src/utils/cepIntegration.ts** | **WebView ↔ ExtendScript glue** | **PATCH** |
| **client/src/stores/settingsStore.ts** | **Calls `listModels` via bridge** | **PATCH** |
| **client/src/components/Modals/ProviderModal.tsx** | **UI (already OK)** | ✅ |

Everything else in the tree stays **untouched**.

---

### 2. 🧩  **Drop-in Bridge – host/api-bridge.jsx**
```javascript
// host/api-bridge.jsx
(function (global) {
  function listModels(providerId, baseURL, apiKey) {
    const url = (baseURL || defaultBaseURL(providerId)) + '/v1/models';
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, false);  // sync → keeps Zustand flow simple
    if (apiKey) xhr.setRequestHeader('Authorization', 'Bearer ' + apiKey);
    xhr.send();
    if (xhr.status !== 200) throw new Error(`HTTP ${xhr.status}`);
    const data = JSON.parse(xhr.responseText);
    return JSON.stringify((data.data || data).map(m => ({
      id: m.id,
      name: m.name || m.id,
      description: m.description || '',
      contextLength: m.context_length || 0,
      isRecommended: m.is_recommended || false
    })));
  }
  function defaultBaseURL(id) {
    const map = {
      openai:'https://api.openai.com',
      anthropic:'https://api.anthropic.com',
      moonshot:'https://api.moonshot.cn',
      groq:'https://api.groq.com',
      deepseek:'https://api.deepseek.com',
      mistral:'https://api.mistral.ai',
      openrouter:'https://openrouter.ai/api',
      perplexity:'https://api.perplexity.ai',
      qwen:'https://dashscope.aliyuncs.com/api',
      together:'https://api.together.xyz',
      vertex:'https://us-central1-aiplatform.googleapis.com/v1/projects/YOUR_PROJECT/locations/us-central1',
      xai:'https://api.x.ai',
      ollama:'http://localhost:11434',
      lmstudio:'http://localhost:1234'
    };
    return map[id] || '';
  }
  global.apiBridge = { listModels };
})(this);
```

---

### 3. 🔗  **Entry Point – host/index.jsx**
```javascript
//@include "api-bridge.jsx"
```

---

### 4. 🔧  **Manifest Patch – CSXS/manifest.xml**
```xml
<Resources>
  <MainPath>./index.html</MainPath>
  <ScriptPath>./host/index.jsx</ScriptPath>   <!-- ← must point to index.jsx -->
</Resources>
```

---

### 5. 🌉  **WebView Glue – client/src/utils/cepIntegration.ts**
```ts
declare const CSInterface: any;

export const ProviderBridge = {
  async listModels(providerId: string, baseURL?: string, apiKey?: string) {
    const cs = new CSInterface();
    const script = `apiBridge.listModels('${providerId}','${baseURL || ''}','${apiKey || ''}')`;
    return new Promise((resolve, reject) => {
      cs.evalScript(script, (result) => {
        if (!result || result === 'EvalScript error.') reject(new Error('Bridge error'));
        else resolve(JSON.parse(result));
      });
    });
  }
};
```

---

### 6. ⚙️  **Store Patch – settingsStore.ts**
Replace the body of `loadModelsForProvider`:

```ts
loadModelsForProvider: async (providerId) => {
  const provider = get().providers.find(p => p.id === providerId);
  if (!provider?.isConfigured) return;

  set(state => ({
    providers: state.providers.map(p =>
      p.id === providerId ? { ...p, isLoading: true, error: undefined } : p
    )
  }));

  try {
    const { ProviderBridge } = await import('../../utils/cepIntegration');
    const raw = await ProviderBridge.listModels(
      providerId,
      provider.baseURL,
      provider.apiKey
    );
    const models: Model[] = raw.map((m: any) => ({
      id: m.id,
      name: m.name,
      description: m.description || '',
      contextLength: m.contextLength || 0,
      isRecommended: m.isRecommended || false
    }));
    get().setProviderModels(providerId, models);
  } catch (e) {
    const friendly = (e as Error).message.includes('401')
      ? 'Invalid API key'
      : (e as Error).message;
    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId ? { ...p, isLoading: false, error: friendly } : p
      )
    }));
  }
},
```

---

### 7. ✅  **Smoke Test**

1. Re-build → `npm run build`  
2. Reload extension → `install-extension.bat`  
3. DevTools console → `evalScript('typeof apiBridge')` → `"object"`  
4. Open **ProviderModal** → pick provider → live models appear

---

### 8. 🎯  **Outcome**

- **Zero hard-coded lists**  
- **Live HTTPS fetch every time**  
- **Works with any provider that supports `/v1/models`**  
- **No modal clipping or selection bugs**

Your CEP extension now behaves **identically to Cline**.