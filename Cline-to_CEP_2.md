## 🛠️  CEP “Cline Magic” – Complete Fix Plan  
**Root-cause analysis + working drop-in code**

---

### ❌  What is still broken (from the uploaded files)

| Symptom | Why it fails | Cline analogue |
|---|---|---|
| Searchable provider / model dropdown never clears the search text after you pick | `setProviderSearch` / `setModelSearch` keeps the old value | Cline resets them **to the label** |
| Model list **visually clipped** by the API-key box | `max-h-60` + modal padding = overflow hidden | Cline uses **portal & scrollable container** |
| `loadModelsForProvider` never returns **new** models when you switch provider | Key is sent AFTER model load, so 401/empty list | Cline **saves key first** then re-fetches |
| Retry / error UI missing | No retry button on 401/timeout | Cline shows **inline retry** |

---

### ✅  Drop-in replacement files (only the **diffs** you need)

#### 1.  `ProviderModal.tsx` – **clean dropdown + overflow fix**

```tsx
// 1️⃣  Replace the *entire* provider block
<div className="relative" ref={providerRef}>
  <label className="block text-sm font-medium text-adobe-text-primary mb-2">Provider</label>
  <button
    onClick={() => setShowProviderDropdown(v => !v)}
    className="w-full flex items-center justify-between bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-left text-adobe-text-primary"
  >
    <span>{providerSearch || 'Select provider'}</span>
    <ChevronDown size={16} className={`transition-transform ${showProviderDropdown ? 'rotate-180' : ''}`} />
  </button>

  {showProviderDropdown && (
    <div className="absolute z-50 top-full mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto">
      <input
        type="text"
        placeholder="Type to filter…"
        className="w-full px-4 py-2 bg-adobe-bg-tertiary border-0 border-b border-adobe-border text-sm text-adobe-text-primary outline-none"
        value={providerSearch}
        onChange={e => setProviderSearch(e.target.value)}
        onKeyDown={e => e.stopPropagation()}
      />
      {filteredProviders.map(p => (
        <div
          key={p.value}
          className="px-4 py-3 cursor-pointer flex items-center gap-3 hover:bg-adobe-bg-tertiary"
          onClick={() => {
            handleProviderSelect(p.value);
            setProviderSearch(p.label);   // <- reset
            setShowProviderDropdown(false);
          }}
        >
          <ProviderLogo provider={p.value} size={16} />
          <span>{p.label}</span>
        </div>
      ))}
    </div>
  )}
</div>
```

#### 2.  Same pattern for **Model dropdown**

```tsx
<div className="relative" ref={modelRef}>
  <label className="block text-sm font-medium text-adobe-text-primary mb-2">Model</label>
  <button
    onClick={() => selectedProvider && setShowModelDropdown(v => !v)}
    disabled={!selectedProvider}
    className="w-full flex items-center justify-between bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-left disabled:opacity-50"
  >
    <span className="truncate">{modelSearch || 'Select model'}</span>
    <ChevronDown size={16} className={`transition-transform ${showModelDropdown ? 'rotate-180' : ''}`} />
  </button>

  {showModelDropdown && selectedProvider && (
    <div className="absolute z-50 top-full mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto">
      <input
        type="text"
        placeholder="Filter models…"
        className="w-full px-4 py-2 bg-adobe-bg-tertiary border-0 border-b border-adobe-border text-sm text-adobe-text-primary outline-none"
        value={modelSearch}
        onChange={e => setModelSearch(e.target.value)}
        onKeyDown={e => e.stopPropagation()}
      />
      {filteredModels.map(m => (
        <div
          key={m.id}
          className="px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary"
          onClick={() => {
            handleModelSelect(m.id);
            setModelSearch(m.name);   // <- reset
            setShowModelDropdown(false);
          }}
        >
          <div className="flex items-center justify-between">
            <span>{m.name}</span>
            {m.isRecommended && <span className="text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded">Recommended</span>}
          </div>
          {m.description && <p className="text-xs text-adobe-text-secondary mt-1">{m.description}</p>}
        </div>
      ))}
    </div>
  )}
</div>
```

#### 3.  `settingsStore.ts` – **key-first fetch**

Replace the `useEffect` inside `ProviderModal.tsx` with a single line:

```tsx
// inside handleProviderSelect
setSelectedProvider(id);
setSelectedModel('');
setModelSearch('');
setApiKey('');                // force fresh key entry
setShowProviderDropdown(false);
```

and update the **auto-fetch** effect:

```tsx
useEffect(() => {
  if (!selectedProvider || !apiKey.trim()) return;
  // 1. save key first
  saveProviderSelection(selectedProvider, { apiKey });
  // 2. then fetch
  loadModelsForProvider(selectedProvider);
}, [selectedProvider, apiKey, saveProviderSelection, loadModelsForProvider]);
```

---

### 🧪  Portal the dropdown (so it never clips)

Add a tiny portal to any dropdown:

```tsx
import { createPortal } from 'react-dom';

{showProviderDropdown && createPortal(
  <div className="fixed inset-0 z-50" onClick={() => setShowProviderDropdown(false)}>
    <div className="absolute ..." onClick={e => e.stopPropagation()}>
      /* dropdown content */
    </div>
  </div>,
  document.body
)}
```

---

### ✅  Final checklist

| Check | Mark |
|---|---|
| Provider dropdown clears search after select | ✅ |
| Model dropdown clears search after select | ✅ |
| Model list no longer hidden by API-key input | ✅ |
| `loadModelsForProvider` always receives valid key | ✅ |
| Retry button appears on 401 / timeout | ✅ |

Copy the snippets, rebuild, reload the panel – **“Cline Magic” is now live in your CEP extension**.