const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./html-C2L_23MC.js","./javascript-ySlJ1b_l.js","./css-BPhBrDlE.js","./php-D3dBWZdZ.js","./xml-e3z08dGr.js","./java-xI-RfyKK.js","./json-BQoSv7ci.js","./ruby-BuaI_Dc4.js","./yaml-CVw76BM1.js","./scss-C31hgJw-.js"])))=>i.map(i=>d[i]);
var yr=Object.defineProperty;var Er=(r,e,t)=>e in r?yr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var _=(r,e,t)=>Er(r,typeof e!="symbol"?e+"":e,t);const vr="modulepreload",Rr=function(r,e){return new URL(r,e).href},ut={},u=function(e,t,n){let i=Promise.resolve();if(t&&t.length>0){const a=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),s=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));i=Promise.allSettled(t.map(c=>{if(c=Rr(c,n),c in ut)return;ut[c]=!0;const m=c.endsWith(".css"),d=m?'[rel="stylesheet"]':"";if(!!n)for(let f=a.length-1;f>=0;f--){const R=a[f];if(R.href===c&&(!m||R.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${d}`))return;const p=document.createElement("link");if(p.rel=m?"stylesheet":vr,m||(p.as="script"),p.crossOrigin="",p.href=c,s&&p.setAttribute("nonce",s),document.head.appendChild(p),m)return new Promise((f,R)=>{p.addEventListener("load",f),p.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(a){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=a,window.dispatchEvent(l),!l.defaultPrevented)throw a}return i.then(a=>{for(const l of a||[])l.status==="rejected"&&o(l.reason);return e().catch(o)})},kt=[{id:"abap",name:"ABAP",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/abap.mjs"),[],import.meta.url)},{id:"actionscript-3",name:"ActionScript",import:()=>u(()=>import("./actionscript-3-D_z4Izcz.js"),[],import.meta.url)},{id:"ada",name:"Ada",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ada.mjs"),[],import.meta.url)},{id:"angular-html",name:"Angular HTML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/angular-html.mjs"),[],import.meta.url)},{id:"angular-ts",name:"Angular TypeScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/angular-ts.mjs"),[],import.meta.url)},{id:"apache",name:"Apache Conf",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/apache.mjs"),[],import.meta.url)},{id:"apex",name:"Apex",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/apex.mjs"),[],import.meta.url)},{id:"apl",name:"APL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/apl.mjs"),[],import.meta.url)},{id:"applescript",name:"AppleScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/applescript.mjs"),[],import.meta.url)},{id:"ara",name:"Ara",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ara.mjs"),[],import.meta.url)},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/asciidoc.mjs"),[],import.meta.url)},{id:"asm",name:"Assembly",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/asm.mjs"),[],import.meta.url)},{id:"astro",name:"Astro",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/astro.mjs"),[],import.meta.url)},{id:"awk",name:"AWK",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/awk.mjs"),[],import.meta.url)},{id:"ballerina",name:"Ballerina",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ballerina.mjs"),[],import.meta.url)},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bat.mjs"),[],import.meta.url)},{id:"beancount",name:"Beancount",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/beancount.mjs"),[],import.meta.url)},{id:"berry",name:"Berry",aliases:["be"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/berry.mjs"),[],import.meta.url)},{id:"bibtex",name:"BibTeX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bibtex.mjs"),[],import.meta.url)},{id:"bicep",name:"Bicep",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bicep.mjs"),[],import.meta.url)},{id:"blade",name:"Blade",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/blade.mjs"),[],import.meta.url)},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bsl.mjs"),[],import.meta.url)},{id:"c",name:"C",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/c.mjs"),[],import.meta.url)},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cadence.mjs"),[],import.meta.url)},{id:"cairo",name:"Cairo",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cairo.mjs"),[],import.meta.url)},{id:"clarity",name:"Clarity",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/clarity.mjs"),[],import.meta.url)},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/clojure.mjs"),[],import.meta.url)},{id:"cmake",name:"CMake",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cmake.mjs"),[],import.meta.url)},{id:"cobol",name:"COBOL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cobol.mjs"),[],import.meta.url)},{id:"codeowners",name:"CODEOWNERS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/codeowners.mjs"),[],import.meta.url)},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/codeql.mjs"),[],import.meta.url)},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/coffee.mjs"),[],import.meta.url)},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/common-lisp.mjs"),[],import.meta.url)},{id:"coq",name:"Coq",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/coq.mjs"),[],import.meta.url)},{id:"cpp",name:"C++",aliases:["c++"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cpp.mjs"),[],import.meta.url)},{id:"crystal",name:"Crystal",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/crystal.mjs"),[],import.meta.url)},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/csharp.mjs"),[],import.meta.url)},{id:"css",name:"CSS",import:()=>u(()=>import("./css-BPhBrDlE.js"),[],import.meta.url)},{id:"csv",name:"CSV",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/csv.mjs"),[],import.meta.url)},{id:"cue",name:"CUE",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cue.mjs"),[],import.meta.url)},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cypher.mjs"),[],import.meta.url)},{id:"d",name:"D",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/d.mjs"),[],import.meta.url)},{id:"dart",name:"Dart",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dart.mjs"),[],import.meta.url)},{id:"dax",name:"DAX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dax.mjs"),[],import.meta.url)},{id:"desktop",name:"Desktop",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/desktop.mjs"),[],import.meta.url)},{id:"diff",name:"Diff",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/diff.mjs"),[],import.meta.url)},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/docker.mjs"),[],import.meta.url)},{id:"dotenv",name:"dotEnv",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dotenv.mjs"),[],import.meta.url)},{id:"dream-maker",name:"Dream Maker",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dream-maker.mjs"),[],import.meta.url)},{id:"edge",name:"Edge",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/edge.mjs"),[],import.meta.url)},{id:"elixir",name:"Elixir",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/elixir.mjs"),[],import.meta.url)},{id:"elm",name:"Elm",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/elm.mjs"),[],import.meta.url)},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/emacs-lisp.mjs"),[],import.meta.url)},{id:"erb",name:"ERB",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/erb.mjs"),[],import.meta.url)},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/erlang.mjs"),[],import.meta.url)},{id:"fennel",name:"Fennel",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fennel.mjs"),[],import.meta.url)},{id:"fish",name:"Fish",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fish.mjs"),[],import.meta.url)},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fluent.mjs"),[],import.meta.url)},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fortran-fixed-form.mjs"),[],import.meta.url)},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fortran-free-form.mjs"),[],import.meta.url)},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fsharp.mjs"),[],import.meta.url)},{id:"gdresource",name:"GDResource",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gdresource.mjs"),[],import.meta.url)},{id:"gdscript",name:"GDScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gdscript.mjs"),[],import.meta.url)},{id:"gdshader",name:"GDShader",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gdshader.mjs"),[],import.meta.url)},{id:"genie",name:"Genie",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/genie.mjs"),[],import.meta.url)},{id:"gherkin",name:"Gherkin",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gherkin.mjs"),[],import.meta.url)},{id:"git-commit",name:"Git Commit Message",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/git-commit.mjs"),[],import.meta.url)},{id:"git-rebase",name:"Git Rebase Message",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/git-rebase.mjs"),[],import.meta.url)},{id:"gleam",name:"Gleam",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gleam.mjs"),[],import.meta.url)},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/glimmer-js.mjs"),[],import.meta.url)},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/glimmer-ts.mjs"),[],import.meta.url)},{id:"glsl",name:"GLSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/glsl.mjs"),[],import.meta.url)},{id:"gnuplot",name:"Gnuplot",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gnuplot.mjs"),[],import.meta.url)},{id:"go",name:"Go",import:()=>u(()=>import("./go-B1SYOhNW.js"),[],import.meta.url)},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/graphql.mjs"),[],import.meta.url)},{id:"groovy",name:"Groovy",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/groovy.mjs"),[],import.meta.url)},{id:"hack",name:"Hack",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hack.mjs"),[],import.meta.url)},{id:"haml",name:"Ruby Haml",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/haml.mjs"),[],import.meta.url)},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/handlebars.mjs"),[],import.meta.url)},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/haskell.mjs"),[],import.meta.url)},{id:"haxe",name:"Haxe",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/haxe.mjs"),[],import.meta.url)},{id:"hcl",name:"HashiCorp HCL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hcl.mjs"),[],import.meta.url)},{id:"hjson",name:"Hjson",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hjson.mjs"),[],import.meta.url)},{id:"hlsl",name:"HLSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hlsl.mjs"),[],import.meta.url)},{id:"html",name:"HTML",import:()=>u(()=>import("./html-C2L_23MC.js"),__vite__mapDeps([0,1,2]),import.meta.url)},{id:"html-derivative",name:"HTML (Derivative)",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/html-derivative.mjs"),[],import.meta.url)},{id:"http",name:"HTTP",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/http.mjs"),[],import.meta.url)},{id:"hxml",name:"HXML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hxml.mjs"),[],import.meta.url)},{id:"hy",name:"Hy",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hy.mjs"),[],import.meta.url)},{id:"imba",name:"Imba",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/imba.mjs"),[],import.meta.url)},{id:"ini",name:"INI",aliases:["properties"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ini.mjs"),[],import.meta.url)},{id:"java",name:"Java",import:()=>u(()=>import("./java-xI-RfyKK.js"),[],import.meta.url)},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>u(()=>import("./javascript-ySlJ1b_l.js"),[],import.meta.url)},{id:"jinja",name:"Jinja",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jinja.mjs"),[],import.meta.url)},{id:"jison",name:"Jison",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jison.mjs"),[],import.meta.url)},{id:"json",name:"JSON",import:()=>u(()=>import("./json-BQoSv7ci.js"),[],import.meta.url)},{id:"json5",name:"JSON5",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/json5.mjs"),[],import.meta.url)},{id:"jsonc",name:"JSON with Comments",import:()=>u(()=>import("./jsonc-TU54ms6u.js"),[],import.meta.url)},{id:"jsonl",name:"JSON Lines",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jsonl.mjs"),[],import.meta.url)},{id:"jsonnet",name:"Jsonnet",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jsonnet.mjs"),[],import.meta.url)},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jssm.mjs"),[],import.meta.url)},{id:"jsx",name:"JSX",import:()=>u(()=>import("./jsx-BAng5TT0.js"),[],import.meta.url)},{id:"julia",name:"Julia",aliases:["jl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/julia.mjs"),[],import.meta.url)},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/kotlin.mjs"),[],import.meta.url)},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/kusto.mjs"),[],import.meta.url)},{id:"latex",name:"LaTeX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/latex.mjs"),[],import.meta.url)},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/lean.mjs"),[],import.meta.url)},{id:"less",name:"Less",import:()=>u(()=>import("./less-BfCpw3nA.js"),[],import.meta.url)},{id:"liquid",name:"Liquid",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/liquid.mjs"),[],import.meta.url)},{id:"log",name:"Log file",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/log.mjs"),[],import.meta.url)},{id:"logo",name:"Logo",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/logo.mjs"),[],import.meta.url)},{id:"lua",name:"Lua",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/lua.mjs"),[],import.meta.url)},{id:"luau",name:"Luau",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/luau.mjs"),[],import.meta.url)},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/make.mjs"),[],import.meta.url)},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>u(()=>import("./markdown-UIAJJxZW.js"),[],import.meta.url)},{id:"marko",name:"Marko",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/marko.mjs"),[],import.meta.url)},{id:"matlab",name:"MATLAB",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/matlab.mjs"),[],import.meta.url)},{id:"mdc",name:"MDC",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mdc.mjs"),[],import.meta.url)},{id:"mdx",name:"MDX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mdx.mjs"),[],import.meta.url)},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mermaid.mjs"),[],import.meta.url)},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mipsasm.mjs"),[],import.meta.url)},{id:"mojo",name:"Mojo",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mojo.mjs"),[],import.meta.url)},{id:"move",name:"Move",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/move.mjs"),[],import.meta.url)},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/narrat.mjs"),[],import.meta.url)},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nextflow.mjs"),[],import.meta.url)},{id:"nginx",name:"Nginx",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nginx.mjs"),[],import.meta.url)},{id:"nim",name:"Nim",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nim.mjs"),[],import.meta.url)},{id:"nix",name:"Nix",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nix.mjs"),[],import.meta.url)},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nushell.mjs"),[],import.meta.url)},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/objective-c.mjs"),[],import.meta.url)},{id:"objective-cpp",name:"Objective-C++",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/objective-cpp.mjs"),[],import.meta.url)},{id:"ocaml",name:"OCaml",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ocaml.mjs"),[],import.meta.url)},{id:"pascal",name:"Pascal",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/pascal.mjs"),[],import.meta.url)},{id:"perl",name:"Perl",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/perl.mjs"),[],import.meta.url)},{id:"php",name:"PHP",import:()=>u(()=>import("./php-D3dBWZdZ.js"),__vite__mapDeps([3,0,1,2,4,5,6]),import.meta.url)},{id:"plsql",name:"PL/SQL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/plsql.mjs"),[],import.meta.url)},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/po.mjs"),[],import.meta.url)},{id:"polar",name:"Polar",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/polar.mjs"),[],import.meta.url)},{id:"postcss",name:"PostCSS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/postcss.mjs"),[],import.meta.url)},{id:"powerquery",name:"PowerQuery",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/powerquery.mjs"),[],import.meta.url)},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/powershell.mjs"),[],import.meta.url)},{id:"prisma",name:"Prisma",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/prisma.mjs"),[],import.meta.url)},{id:"prolog",name:"Prolog",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/prolog.mjs"),[],import.meta.url)},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/proto.mjs"),[],import.meta.url)},{id:"pug",name:"Pug",aliases:["jade"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/pug.mjs"),[],import.meta.url)},{id:"puppet",name:"Puppet",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/puppet.mjs"),[],import.meta.url)},{id:"purescript",name:"PureScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/purescript.mjs"),[],import.meta.url)},{id:"python",name:"Python",aliases:["py"],import:()=>u(()=>import("./python-DhUJRlN_.js"),[],import.meta.url)},{id:"qml",name:"QML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/qml.mjs"),[],import.meta.url)},{id:"qmldir",name:"QML Directory",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/qmldir.mjs"),[],import.meta.url)},{id:"qss",name:"Qt Style Sheets",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/qss.mjs"),[],import.meta.url)},{id:"r",name:"R",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/r.mjs"),[],import.meta.url)},{id:"racket",name:"Racket",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/racket.mjs"),[],import.meta.url)},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/raku.mjs"),[],import.meta.url)},{id:"razor",name:"ASP.NET Razor",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/razor.mjs"),[],import.meta.url)},{id:"reg",name:"Windows Registry Script",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/reg.mjs"),[],import.meta.url)},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/regexp.mjs"),[],import.meta.url)},{id:"rel",name:"Rel",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/rel.mjs"),[],import.meta.url)},{id:"riscv",name:"RISC-V",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/riscv.mjs"),[],import.meta.url)},{id:"rst",name:"reStructuredText",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/rst.mjs"),[],import.meta.url)},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>u(()=>import("./ruby-BuaI_Dc4.js"),__vite__mapDeps([7,0,1,2,4,5,8]),import.meta.url)},{id:"rust",name:"Rust",aliases:["rs"],import:()=>u(()=>import("./rust-Be6lgOlo.js"),[],import.meta.url)},{id:"sas",name:"SAS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sas.mjs"),[],import.meta.url)},{id:"sass",name:"Sass",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sass.mjs"),[],import.meta.url)},{id:"scala",name:"Scala",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/scala.mjs"),[],import.meta.url)},{id:"scheme",name:"Scheme",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/scheme.mjs"),[],import.meta.url)},{id:"scss",name:"SCSS",import:()=>u(()=>import("./scss-C31hgJw-.js"),__vite__mapDeps([9,2]),import.meta.url)},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sdbl.mjs"),[],import.meta.url)},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/shaderlab.mjs"),[],import.meta.url)},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/shellscript.mjs"),[],import.meta.url)},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/shellsession.mjs"),[],import.meta.url)},{id:"smalltalk",name:"Smalltalk",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/smalltalk.mjs"),[],import.meta.url)},{id:"solidity",name:"Solidity",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/solidity.mjs"),[],import.meta.url)},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/soy.mjs"),[],import.meta.url)},{id:"sparql",name:"SPARQL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sparql.mjs"),[],import.meta.url)},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/splunk.mjs"),[],import.meta.url)},{id:"sql",name:"SQL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sql.mjs"),[],import.meta.url)},{id:"ssh-config",name:"SSH Config",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ssh-config.mjs"),[],import.meta.url)},{id:"stata",name:"Stata",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/stata.mjs"),[],import.meta.url)},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/stylus.mjs"),[],import.meta.url)},{id:"svelte",name:"Svelte",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/svelte.mjs"),[],import.meta.url)},{id:"swift",name:"Swift",import:()=>u(()=>import("./swift-BSxZ-RaX.js"),[],import.meta.url)},{id:"system-verilog",name:"SystemVerilog",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/system-verilog.mjs"),[],import.meta.url)},{id:"systemd",name:"Systemd Units",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/systemd.mjs"),[],import.meta.url)},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/talonscript.mjs"),[],import.meta.url)},{id:"tasl",name:"Tasl",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tasl.mjs"),[],import.meta.url)},{id:"tcl",name:"Tcl",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tcl.mjs"),[],import.meta.url)},{id:"templ",name:"Templ",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/templ.mjs"),[],import.meta.url)},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/terraform.mjs"),[],import.meta.url)},{id:"tex",name:"TeX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tex.mjs"),[],import.meta.url)},{id:"toml",name:"TOML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/toml.mjs"),[],import.meta.url)},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ts-tags.mjs"),[],import.meta.url)},{id:"tsv",name:"TSV",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tsv.mjs"),[],import.meta.url)},{id:"tsx",name:"TSX",import:()=>u(()=>import("./tsx-B6W0miNI.js"),[],import.meta.url)},{id:"turtle",name:"Turtle",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/turtle.mjs"),[],import.meta.url)},{id:"twig",name:"Twig",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/twig.mjs"),[],import.meta.url)},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>u(()=>import("./typescript-Dj6nwHGl.js"),[],import.meta.url)},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/typespec.mjs"),[],import.meta.url)},{id:"typst",name:"Typst",aliases:["typ"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/typst.mjs"),[],import.meta.url)},{id:"v",name:"V",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/v.mjs"),[],import.meta.url)},{id:"vala",name:"Vala",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vala.mjs"),[],import.meta.url)},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vb.mjs"),[],import.meta.url)},{id:"verilog",name:"Verilog",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/verilog.mjs"),[],import.meta.url)},{id:"vhdl",name:"VHDL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vhdl.mjs"),[],import.meta.url)},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/viml.mjs"),[],import.meta.url)},{id:"vue",name:"Vue",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vue.mjs"),[],import.meta.url)},{id:"vue-html",name:"Vue HTML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vue-html.mjs"),[],import.meta.url)},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vyper.mjs"),[],import.meta.url)},{id:"wasm",name:"WebAssembly",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wasm.mjs"),[],import.meta.url)},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wenyan.mjs"),[],import.meta.url)},{id:"wgsl",name:"WGSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wgsl.mjs"),[],import.meta.url)},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wikitext.mjs"),[],import.meta.url)},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wolfram.mjs"),[],import.meta.url)},{id:"xml",name:"XML",import:()=>u(()=>import("./xml-e3z08dGr.js"),__vite__mapDeps([4,5]),import.meta.url)},{id:"xsl",name:"XSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/xsl.mjs"),[],import.meta.url)},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>u(()=>import("./yaml-CVw76BM1.js"),[],import.meta.url)},{id:"zenscript",name:"ZenScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/zenscript.mjs"),[],import.meta.url)},{id:"zig",name:"Zig",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/zig.mjs"),[],import.meta.url)}],Ar=Object.fromEntries(kt.map(r=>[r.id,r.import])),br=Object.fromEntries(kt.flatMap(r=>{var e;return((e=r.aliases)==null?void 0:e.map(t=>[t,r.import]))||[]})),Pr={...Ar,...br},Tr=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>u(()=>import("./andromeeda-C3khCPGq.js"),[],import.meta.url)},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>u(()=>import("./aurora-x-D-2ljcwZ.js"),[],import.meta.url)},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>u(()=>import("./ayu-dark-Cv9koXgw.js"),[],import.meta.url)},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>u(()=>import("./catppuccin-frappe-CD_QflpE.js"),[],import.meta.url)},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>u(()=>import("./catppuccin-latte-DRW-0cLl.js"),[],import.meta.url)},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>u(()=>import("./catppuccin-macchiato-C-_shW-Y.js"),[],import.meta.url)},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>u(()=>import("./catppuccin-mocha-LGGdnPYs.js"),[],import.meta.url)},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>u(()=>import("./dark-plus-C3mMm8J8.js"),[],import.meta.url)},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>u(()=>import("./dracula-BzJJZx-M.js"),[],import.meta.url)},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>u(()=>import("./dracula-soft-BXkSAIEj.js"),[],import.meta.url)},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>u(()=>import("./everforest-dark-BgDCqdQA.js"),[],import.meta.url)},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>u(()=>import("./everforest-light-C8M2exoo.js"),[],import.meta.url)},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>u(()=>import("./github-dark-DHJKELXO.js"),[],import.meta.url)},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>u(()=>import("./github-dark-default-Cuk6v7N8.js"),[],import.meta.url)},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>u(()=>import("./github-dark-dimmed-DH5Ifo-i.js"),[],import.meta.url)},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>u(()=>import("./github-dark-high-contrast-E3gJ1_iC.js"),[],import.meta.url)},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>u(()=>import("./github-light-DAi9KRSo.js"),[],import.meta.url)},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>u(()=>import("./github-light-default-D7oLnXFd.js"),[],import.meta.url)},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>u(()=>import("./github-light-high-contrast-BfjtVDDH.js"),[],import.meta.url)},{id:"houston",displayName:"Houston",type:"dark",import:()=>u(()=>import("./houston-DnULxvSX.js"),[],import.meta.url)},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>u(()=>import("./kanagawa-dragon-CkXjmgJE.js"),[],import.meta.url)},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>u(()=>import("./kanagawa-lotus-CfQXZHmo.js"),[],import.meta.url)},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>u(()=>import("./kanagawa-wave-DWedfzmr.js"),[],import.meta.url)},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>u(()=>import("./laserwave-DUszq2jm.js"),[],import.meta.url)},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>u(()=>import("./light-plus-B7mTdjB0.js"),[],import.meta.url)},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>u(()=>import("./material-theme-D5KoaKCx.js"),[],import.meta.url)},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>u(()=>import("./material-theme-darker-BfHTSMKl.js"),[],import.meta.url)},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>u(()=>import("./material-theme-lighter-B0m2ddpp.js"),[],import.meta.url)},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>u(()=>import("./material-theme-ocean-CyktbL80.js"),[],import.meta.url)},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>u(()=>import("./material-theme-palenight-Csfq5Kiy.js"),[],import.meta.url)},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>u(()=>import("./min-dark-CafNBF8u.js"),[],import.meta.url)},{id:"min-light",displayName:"Min Light",type:"light",import:()=>u(()=>import("./min-light-CTRr51gU.js"),[],import.meta.url)},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>u(()=>import("./monokai-D4h5O-jR.js"),[],import.meta.url)},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>u(()=>import("./night-owl-C39BiMTA.js"),[],import.meta.url)},{id:"nord",displayName:"Nord",type:"dark",import:()=>u(()=>import("./nord-Ddv68eIx.js"),[],import.meta.url)},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>u(()=>import("./one-dark-pro-GBQ2dnAY.js"),[],import.meta.url)},{id:"one-light",displayName:"One Light",type:"light",import:()=>u(()=>import("./one-light-PoHY5YXO.js"),[],import.meta.url)},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>u(()=>import("./plastic-3e1v2bzS.js"),[],import.meta.url)},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>u(()=>import("./poimandres-CS3Unz2-.js"),[],import.meta.url)},{id:"red",displayName:"Red",type:"dark",import:()=>u(()=>import("./red-bN70gL4F.js"),[],import.meta.url)},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>u(()=>import("./rose-pine-CmCqftbK.js"),[],import.meta.url)},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>u(()=>import("./rose-pine-dawn-Ds-gbosJ.js"),[],import.meta.url)},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>u(()=>import("./rose-pine-moon-CjDtw9vr.js"),[],import.meta.url)},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>u(()=>import("./slack-dark-BthQWCQV.js"),[],import.meta.url)},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>u(()=>import("./slack-ochin-DqwNpetd.js"),[],import.meta.url)},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>u(()=>import("./snazzy-light-Bw305WKR.js"),[],import.meta.url)},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>u(()=>import("./solarized-dark-DXbdFlpD.js"),[],import.meta.url)},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>u(()=>import("./solarized-light-L9t79GZl.js"),[],import.meta.url)},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>u(()=>import("./synthwave-84-CbfX1IO0.js"),[],import.meta.url)},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>u(()=>import("./tokyo-night-DBQeEorK.js"),[],import.meta.url)},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>u(()=>import("./vesper-BEBZ7ncR.js"),[],import.meta.url)},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>u(()=>import("./vitesse-black-Bkuqu6BP.js"),[],import.meta.url)},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>u(()=>import("./vitesse-dark-D0r3Knsf.js"),[],import.meta.url)},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>u(()=>import("./vitesse-light-CVO1_9PV.js"),[],import.meta.url)}],Lr=Object.fromEntries(Tr.map(r=>[r.id,r.import]));let F=class extends Error{constructor(e){super(e),this.name="ShikiError"}},Qe=class extends Error{constructor(e){super(e),this.name="ShikiError"}};function Sr(){return 2147483648}function Ir(){return typeof performance<"u"?performance.now():Date.now()}const Or=(r,e)=>r+(e-r%e)%e;async function Cr(r){let e,t;const n={};function i(p){t=p,n.HEAPU8=new Uint8Array(p),n.HEAPU32=new Uint32Array(p)}function o(p,f,R){n.HEAPU8.copyWithin(p,f,f+R)}function a(p){try{return e.grow(p-t.byteLength+65535>>>16),i(e.buffer),1}catch{}}function l(p){const f=n.HEAPU8.length;p=p>>>0;const R=Sr();if(p>R)return!1;for(let E=1;E<=4;E*=2){let y=f*(1+.2/E);y=Math.min(y,p+100663296);const v=Math.min(R,Or(Math.max(p,y),65536));if(a(v))return!0}return!1}const s=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function c(p,f,R=1024){const E=f+R;let y=f;for(;p[y]&&!(y>=E);)++y;if(y-f>16&&p.buffer&&s)return s.decode(p.subarray(f,y));let v="";for(;f<y;){let A=p[f++];if(!(A&128)){v+=String.fromCharCode(A);continue}const P=p[f++]&63;if((A&224)===192){v+=String.fromCharCode((A&31)<<6|P);continue}const L=p[f++]&63;if((A&240)===224?A=(A&15)<<12|P<<6|L:A=(A&7)<<18|P<<12|L<<6|p[f++]&63,A<65536)v+=String.fromCharCode(A);else{const N=A-65536;v+=String.fromCharCode(55296|N>>10,56320|N&1023)}}return v}function m(p,f){return p?c(n.HEAPU8,p,f):""}const d={emscripten_get_now:Ir,emscripten_memcpy_big:o,emscripten_resize_heap:l,fd_write:()=>0};async function h(){const f=await r({env:d,wasi_snapshot_preview1:d});e=f.memory,i(e.buffer),Object.assign(n,f),n.UTF8ToString=m}return await h(),n}var wr=Object.defineProperty,kr=(r,e,t)=>e in r?wr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,O=(r,e,t)=>(kr(r,typeof e!="symbol"?e+"":e,t),t);let w=null;function Dr(r){throw new Qe(r.UTF8ToString(r.getLastOnigError()))}class Oe{constructor(e){O(this,"utf16Length"),O(this,"utf8Length"),O(this,"utf16Value"),O(this,"utf8Value"),O(this,"utf16OffsetToUtf8"),O(this,"utf8OffsetToUtf16");const t=e.length,n=Oe._utf8ByteLength(e),i=n!==t,o=i?new Uint32Array(t+1):null;i&&(o[t]=n);const a=i?new Uint32Array(n+1):null;i&&(a[n]=t);const l=new Uint8Array(n);let s=0;for(let c=0;c<t;c++){const m=e.charCodeAt(c);let d=m,h=!1;if(m>=55296&&m<=56319&&c+1<t){const p=e.charCodeAt(c+1);p>=56320&&p<=57343&&(d=(m-55296<<10)+65536|p-56320,h=!0)}i&&(o[c]=s,h&&(o[c+1]=s),d<=127?a[s+0]=c:d<=2047?(a[s+0]=c,a[s+1]=c):d<=65535?(a[s+0]=c,a[s+1]=c,a[s+2]=c):(a[s+0]=c,a[s+1]=c,a[s+2]=c,a[s+3]=c)),d<=127?l[s++]=d:d<=2047?(l[s++]=192|(d&1984)>>>6,l[s++]=128|(d&63)>>>0):d<=65535?(l[s++]=224|(d&61440)>>>12,l[s++]=128|(d&4032)>>>6,l[s++]=128|(d&63)>>>0):(l[s++]=240|(d&1835008)>>>18,l[s++]=128|(d&258048)>>>12,l[s++]=128|(d&4032)>>>6,l[s++]=128|(d&63)>>>0),h&&c++}this.utf16Length=t,this.utf8Length=n,this.utf16Value=e,this.utf8Value=l,this.utf16OffsetToUtf8=o,this.utf8OffsetToUtf16=a}static _utf8ByteLength(e){let t=0;for(let n=0,i=e.length;n<i;n++){const o=e.charCodeAt(n);let a=o,l=!1;if(o>=55296&&o<=56319&&n+1<i){const s=e.charCodeAt(n+1);s>=56320&&s<=57343&&(a=(o-55296<<10)+65536|s-56320,l=!0)}a<=127?t+=1:a<=2047?t+=2:a<=65535?t+=3:t+=4,l&&n++}return t}createString(e){const t=e.omalloc(this.utf8Length);return e.HEAPU8.set(this.utf8Value,t),t}}const G=class{constructor(r){if(O(this,"id",++G.LAST_ID),O(this,"_onigBinding"),O(this,"content"),O(this,"utf16Length"),O(this,"utf8Length"),O(this,"utf16OffsetToUtf8"),O(this,"utf8OffsetToUtf16"),O(this,"ptr"),!w)throw new Qe("Must invoke loadWasm first.");this._onigBinding=w,this.content=r;const e=new Oe(r);this.utf16Length=e.utf16Length,this.utf8Length=e.utf8Length,this.utf16OffsetToUtf8=e.utf16OffsetToUtf8,this.utf8OffsetToUtf16=e.utf8OffsetToUtf16,this.utf8Length<1e4&&!G._sharedPtrInUse?(G._sharedPtr||(G._sharedPtr=w.omalloc(1e4)),G._sharedPtrInUse=!0,w.HEAPU8.set(e.utf8Value,G._sharedPtr),this.ptr=G._sharedPtr):this.ptr=e.createString(w)}convertUtf8OffsetToUtf16(r){return this.utf8OffsetToUtf16?r<0?0:r>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[r]:r}convertUtf16OffsetToUtf8(r){return this.utf16OffsetToUtf8?r<0?0:r>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[r]:r}dispose(){this.ptr===G._sharedPtr?G._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let ue=G;O(ue,"LAST_ID",0);O(ue,"_sharedPtr",0);O(ue,"_sharedPtrInUse",!1);class Nr{constructor(e){if(O(this,"_onigBinding"),O(this,"_ptr"),!w)throw new Qe("Must invoke loadWasm first.");const t=[],n=[];for(let l=0,s=e.length;l<s;l++){const c=new Oe(e[l]);t[l]=c.createString(w),n[l]=c.utf8Length}const i=w.omalloc(4*e.length);w.HEAPU32.set(t,i/4);const o=w.omalloc(4*e.length);w.HEAPU32.set(n,o/4);const a=w.createOnigScanner(i,o,e.length);for(let l=0,s=e.length;l<s;l++)w.ofree(t[l]);w.ofree(o),w.ofree(i),a===0&&Dr(w),this._onigBinding=w,this._ptr=a}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(e,t,n){let i=0;if(typeof n=="number"&&(i=n),typeof e=="string"){e=new ue(e);const o=this._findNextMatchSync(e,t,!1,i);return e.dispose(),o}return this._findNextMatchSync(e,t,!1,i)}_findNextMatchSync(e,t,n,i){const o=this._onigBinding,a=o.findNextOnigScannerMatch(this._ptr,e.id,e.ptr,e.utf8Length,e.convertUtf16OffsetToUtf8(t),i);if(a===0)return null;const l=o.HEAPU32;let s=a/4;const c=l[s++],m=l[s++],d=[];for(let h=0;h<m;h++){const p=e.convertUtf8OffsetToUtf16(l[s++]),f=e.convertUtf8OffsetToUtf16(l[s++]);d[h]={start:p,end:f,length:f-p}}return{index:c,captureIndices:d}}}function xr(r){return typeof r.instantiator=="function"}function Vr(r){return typeof r.default=="function"}function Mr(r){return typeof r.data<"u"}function Br(r){return typeof Response<"u"&&r instanceof Response}function Gr(r){var e;return typeof ArrayBuffer<"u"&&(r instanceof ArrayBuffer||ArrayBuffer.isView(r))||typeof Buffer<"u"&&((e=Buffer.isBuffer)==null?void 0:e.call(Buffer,r))||typeof SharedArrayBuffer<"u"&&r instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&r instanceof Uint32Array}let de;function jr(r){if(de)return de;async function e(){w=await Cr(async t=>{let n=r;return n=await n,typeof n=="function"&&(n=await n(t)),typeof n=="function"&&(n=await n(t)),xr(n)?n=await n.instantiator(t):Vr(n)?n=await n.default(t):(Mr(n)&&(n=n.data),Br(n)?typeof WebAssembly.instantiateStreaming=="function"?n=await Ur(n)(t):n=await $r(n)(t):Gr(n)?n=await Ne(n)(t):n instanceof WebAssembly.Module?n=await Ne(n)(t):"default"in n&&n.default instanceof WebAssembly.Module&&(n=await Ne(n.default)(t))),"instance"in n&&(n=n.instance),"exports"in n&&(n=n.exports),n})}return de=e(),de}function Ne(r){return e=>WebAssembly.instantiate(r,e)}function Ur(r){return e=>WebAssembly.instantiateStreaming(r,e)}function $r(r){return async e=>{const t=await r.arrayBuffer();return WebAssembly.instantiate(t,e)}}let Wr;function Hr(){return Wr}async function Dt(r){return r&&await jr(r),{createScanner(e){return new Nr(e.map(t=>typeof t=="string"?t:t.source))},createString(e){return new ue(e)}}}function Fr(r){return Ze(r)}function Ze(r){return Array.isArray(r)?zr(r):r instanceof RegExp?r:typeof r=="object"?qr(r):r}function zr(r){let e=[];for(let t=0,n=r.length;t<n;t++)e[t]=Ze(r[t]);return e}function qr(r){let e={};for(let t in r)e[t]=Ze(r[t]);return e}function Nt(r,...e){return e.forEach(t=>{for(let n in t)r[n]=t[n]}),r}function xt(r){const e=~r.lastIndexOf("/")||~r.lastIndexOf("\\");return e===0?r:~e===r.length-1?xt(r.substring(0,r.length-1)):r.substr(~e+1)}var xe=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,he=class{static hasCaptures(r){return r===null?!1:(xe.lastIndex=0,xe.test(r))}static replaceCaptures(r,e,t){return r.replace(xe,(n,i,o,a)=>{let l=t[parseInt(i||o,10)];if(l){let s=e.substring(l.start,l.end);for(;s[0]===".";)s=s.substring(1);switch(a){case"downcase":return s.toLowerCase();case"upcase":return s.toUpperCase();default:return s}}else return n})}};function Vt(r,e){return r<e?-1:r>e?1:0}function Mt(r,e){if(r===null&&e===null)return 0;if(!r)return-1;if(!e)return 1;let t=r.length,n=e.length;if(t===n){for(let i=0;i<t;i++){let o=Vt(r[i],e[i]);if(o!==0)return o}return 0}return t-n}function ct(r){return!!(/^#[0-9a-f]{6}$/i.test(r)||/^#[0-9a-f]{8}$/i.test(r)||/^#[0-9a-f]{3}$/i.test(r)||/^#[0-9a-f]{4}$/i.test(r))}function Bt(r){return r.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var Gt=class{constructor(r){_(this,"cache",new Map);this.fn=r}get(r){if(this.cache.has(r))return this.cache.get(r);const e=this.fn(r);return this.cache.set(r,e),e}},Ee=class{constructor(r,e,t){_(this,"_cachedMatchRoot",new Gt(r=>this._root.match(r)));this._colorMap=r,this._defaults=e,this._root=t}static createFromRawTheme(r,e){return this.createFromParsedTheme(Xr(r),e)}static createFromParsedTheme(r,e){return Qr(r,e)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(r){if(r===null)return this._defaults;const e=r.scopeName,n=this._cachedMatchRoot.get(e).find(i=>Kr(r.parent,i.parentScopes));return n?new jt(n.fontStyle,n.foreground,n.background):null}},Ve=class ge{constructor(e,t){this.parent=e,this.scopeName=t}static push(e,t){for(const n of t)e=new ge(e,n);return e}static from(...e){let t=null;for(let n=0;n<e.length;n++)t=new ge(t,e[n]);return t}push(e){return new ge(this,e)}getSegments(){let e=this;const t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e?!0:this.parent===null?!1:this.parent.extends(e)}getExtensionIfDefined(e){const t=[];let n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}};function Kr(r,e){if(e.length===0)return!0;for(let t=0;t<e.length;t++){let n=e[t],i=!1;if(n===">"){if(t===e.length-1)return!1;n=e[++t],i=!0}for(;r&&!Jr(r.scopeName,n);){if(i)return!1;r=r.parent}if(!r)return!1;r=r.parent}return!0}function Jr(r,e){return e===r||r.startsWith(e)&&r[e.length]==="."}var jt=class{constructor(r,e,t){this.fontStyle=r,this.foregroundId=e,this.backgroundId=t}};function Xr(r){if(!r)return[];if(!r.settings||!Array.isArray(r.settings))return[];let e=r.settings,t=[],n=0;for(let i=0,o=e.length;i<o;i++){let a=e[i];if(!a.settings)continue;let l;if(typeof a.scope=="string"){let d=a.scope;d=d.replace(/^[,]+/,""),d=d.replace(/[,]+$/,""),l=d.split(",")}else Array.isArray(a.scope)?l=a.scope:l=[""];let s=-1;if(typeof a.settings.fontStyle=="string"){s=0;let d=a.settings.fontStyle.split(" ");for(let h=0,p=d.length;h<p;h++)switch(d[h]){case"italic":s=s|1;break;case"bold":s=s|2;break;case"underline":s=s|4;break;case"strikethrough":s=s|8;break}}let c=null;typeof a.settings.foreground=="string"&&ct(a.settings.foreground)&&(c=a.settings.foreground);let m=null;typeof a.settings.background=="string"&&ct(a.settings.background)&&(m=a.settings.background);for(let d=0,h=l.length;d<h;d++){let f=l[d].trim().split(" "),R=f[f.length-1],E=null;f.length>1&&(E=f.slice(0,f.length-1),E.reverse()),t[n++]=new Yr(R,E,i,s,c,m)}}return t}var Yr=class{constructor(r,e,t,n,i,o){this.scope=r,this.parentScopes=e,this.index=t,this.fontStyle=n,this.foreground=i,this.background=o}},H=(r=>(r[r.NotSet=-1]="NotSet",r[r.None=0]="None",r[r.Italic=1]="Italic",r[r.Bold=2]="Bold",r[r.Underline=4]="Underline",r[r.Strikethrough=8]="Strikethrough",r))(H||{});function Qr(r,e){r.sort((s,c)=>{let m=Vt(s.scope,c.scope);return m!==0||(m=Mt(s.parentScopes,c.parentScopes),m!==0)?m:s.index-c.index});let t=0,n="#000000",i="#ffffff";for(;r.length>=1&&r[0].scope==="";){let s=r.shift();s.fontStyle!==-1&&(t=s.fontStyle),s.foreground!==null&&(n=s.foreground),s.background!==null&&(i=s.background)}let o=new Zr(e),a=new jt(t,o.getId(n),o.getId(i)),l=new tn(new $e(0,null,-1,0,0),[]);for(let s=0,c=r.length;s<c;s++){let m=r[s];l.insert(0,m.scope,m.parentScopes,m.fontStyle,o.getId(m.foreground),o.getId(m.background))}return new Ee(o,a,l)}var Zr=class{constructor(r){_(this,"_isFrozen");_(this,"_lastColorId");_(this,"_id2color");_(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(r)){this._isFrozen=!0;for(let e=0,t=r.length;e<t;e++)this._color2id[r[e]]=e,this._id2color[e]=r[e]}else this._isFrozen=!1}getId(r){if(r===null)return 0;r=r.toUpperCase();let e=this._color2id[r];if(e)return e;if(this._isFrozen)throw new Error(`Missing color in color map - ${r}`);return e=++this._lastColorId,this._color2id[r]=e,this._id2color[e]=r,e}getColorMap(){return this._id2color.slice(0)}},en=Object.freeze([]),$e=class Ut{constructor(e,t,n,i,o){_(this,"scopeDepth");_(this,"parentScopes");_(this,"fontStyle");_(this,"foreground");_(this,"background");this.scopeDepth=e,this.parentScopes=t||en,this.fontStyle=n,this.foreground=i,this.background=o}clone(){return new Ut(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,i=e.length;n<i;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,i){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,t!==-1&&(this.fontStyle=t),n!==0&&(this.foreground=n),i!==0&&(this.background=i)}},tn=class We{constructor(e,t=[],n={}){_(this,"_rulesWithParentScopes");this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,i=0;for(;e.parentScopes[n]===">"&&n++,t.parentScopes[i]===">"&&i++,!(n>=e.parentScopes.length||i>=t.parentScopes.length);){const o=t.parentScopes[i].length-e.parentScopes[n].length;if(o!==0)return o;n++,i++}return t.parentScopes.length-e.parentScopes.length}match(e){if(e!==""){let n=e.indexOf("."),i,o;if(n===-1?(i=e,o=""):(i=e.substring(0,n),o=e.substring(n+1)),this._children.hasOwnProperty(i))return this._children[i].match(o)}const t=this._rulesWithParentScopes.concat(this._mainRule);return t.sort(We._cmpBySpecificity),t}insert(e,t,n,i,o,a){if(t===""){this._doInsertHere(e,n,i,o,a);return}let l=t.indexOf("."),s,c;l===-1?(s=t,c=""):(s=t.substring(0,l),c=t.substring(l+1));let m;this._children.hasOwnProperty(s)?m=this._children[s]:(m=new We(this._mainRule.clone(),$e.cloneArr(this._rulesWithParentScopes)),this._children[s]=m),m.insert(e+1,c,n,i,o,a)}_doInsertHere(e,t,n,i,o){if(t===null){this._mainRule.acceptOverwrite(e,n,i,o);return}for(let a=0,l=this._rulesWithParentScopes.length;a<l;a++){let s=this._rulesWithParentScopes[a];if(Mt(s.parentScopes,t)===0){s.acceptOverwrite(e,n,i,o);return}}n===-1&&(n=this._mainRule.fontStyle),i===0&&(i=this._mainRule.foreground),o===0&&(o=this._mainRule.background),this._rulesWithParentScopes.push(new $e(e,t,n,i,o))}},Q=class M{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(e){const t=M.getLanguageId(e),n=M.getTokenType(e),i=M.getFontStyle(e),o=M.getForeground(e),a=M.getBackground(e);console.log({languageId:t,tokenType:n,fontStyle:i,foreground:o,background:a})}static getLanguageId(e){return(e&255)>>>0}static getTokenType(e){return(e&768)>>>8}static containsBalancedBrackets(e){return(e&1024)!==0}static getFontStyle(e){return(e&30720)>>>11}static getForeground(e){return(e&16744448)>>>15}static getBackground(e){return(e&4278190080)>>>24}static set(e,t,n,i,o,a,l){let s=M.getLanguageId(e),c=M.getTokenType(e),m=M.containsBalancedBrackets(e)?1:0,d=M.getFontStyle(e),h=M.getForeground(e),p=M.getBackground(e);return t!==0&&(s=t),n!==8&&(c=n),i!==null&&(m=i?1:0),o!==-1&&(d=o),a!==0&&(h=a),l!==0&&(p=l),(s<<0|c<<8|m<<10|d<<11|h<<15|p<<24)>>>0}};function ve(r,e){const t=[],n=rn(r);let i=n.next();for(;i!==null;){let s=0;if(i.length===2&&i.charAt(1)===":"){switch(i.charAt(0)){case"R":s=1;break;case"L":s=-1;break;default:console.log(`Unknown priority ${i} in scope selector`)}i=n.next()}let c=a();if(t.push({matcher:c,priority:s}),i!==",")break;i=n.next()}return t;function o(){if(i==="-"){i=n.next();const s=o();return c=>!!s&&!s(c)}if(i==="("){i=n.next();const s=l();return i===")"&&(i=n.next()),s}if(mt(i)){const s=[];do s.push(i),i=n.next();while(mt(i));return c=>e(s,c)}return null}function a(){const s=[];let c=o();for(;c;)s.push(c),c=o();return m=>s.every(d=>d(m))}function l(){const s=[];let c=a();for(;c&&(s.push(c),i==="|"||i===",");){do i=n.next();while(i==="|"||i===",");c=a()}return m=>s.some(d=>d(m))}}function mt(r){return!!r&&!!r.match(/[\w\.:]+/)}function rn(r){let e=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,t=e.exec(r);return{next:()=>{if(!t)return null;const n=t[0];return t=e.exec(r),n}}}function $t(r){typeof r.dispose=="function"&&r.dispose()}var oe=class{constructor(r){this.scopeName=r}toKey(){return this.scopeName}},nn=class{constructor(r,e){this.scopeName=r,this.ruleName=e}toKey(){return`${this.scopeName}#${this.ruleName}`}},on=class{constructor(){_(this,"_references",[]);_(this,"_seenReferenceKeys",new Set);_(this,"visitedRule",new Set)}get references(){return this._references}add(r){const e=r.toKey();this._seenReferenceKeys.has(e)||(this._seenReferenceKeys.add(e),this._references.push(r))}},an=class{constructor(r,e){_(this,"seenFullScopeRequests",new Set);_(this,"seenPartialScopeRequests",new Set);_(this,"Q");this.repo=r,this.initialScopeName=e,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new oe(this.initialScopeName)]}processQueue(){const r=this.Q;this.Q=[];const e=new on;for(const t of r)sn(t,this.initialScopeName,this.repo,e);for(const t of e.references)if(t instanceof oe){if(this.seenFullScopeRequests.has(t.scopeName))continue;this.seenFullScopeRequests.add(t.scopeName),this.Q.push(t)}else{if(this.seenFullScopeRequests.has(t.scopeName)||this.seenPartialScopeRequests.has(t.toKey()))continue;this.seenPartialScopeRequests.add(t.toKey()),this.Q.push(t)}}};function sn(r,e,t,n){const i=t.lookup(r.scopeName);if(!i){if(r.scopeName===e)throw new Error(`No grammar provided for <${e}>`);return}const o=t.lookup(e);r instanceof oe?ye({baseGrammar:o,selfGrammar:i},n):He(r.ruleName,{baseGrammar:o,selfGrammar:i,repository:i.repository},n);const a=t.injections(r.scopeName);if(a)for(const l of a)n.add(new oe(l))}function He(r,e,t){if(e.repository&&e.repository[r]){const n=e.repository[r];Re([n],e,t)}}function ye(r,e){r.selfGrammar.patterns&&Array.isArray(r.selfGrammar.patterns)&&Re(r.selfGrammar.patterns,{...r,repository:r.selfGrammar.repository},e),r.selfGrammar.injections&&Re(Object.values(r.selfGrammar.injections),{...r,repository:r.selfGrammar.repository},e)}function Re(r,e,t){for(const n of r){if(t.visitedRule.has(n))continue;t.visitedRule.add(n);const i=n.repository?Nt({},e.repository,n.repository):e.repository;Array.isArray(n.patterns)&&Re(n.patterns,{...e,repository:i},t);const o=n.include;if(!o)continue;const a=Wt(o);switch(a.kind){case 0:ye({...e,selfGrammar:e.baseGrammar},t);break;case 1:ye(e,t);break;case 2:He(a.ruleName,{...e,repository:i},t);break;case 3:case 4:const l=a.scopeName===e.selfGrammar.scopeName?e.selfGrammar:a.scopeName===e.baseGrammar.scopeName?e.baseGrammar:void 0;if(l){const s={baseGrammar:e.baseGrammar,selfGrammar:l,repository:i};a.kind===4?He(a.ruleName,s,t):ye(s,t)}else a.kind===4?t.add(new nn(a.scopeName,a.ruleName)):t.add(new oe(a.scopeName));break}}}var ln=class{constructor(){_(this,"kind",0)}},un=class{constructor(){_(this,"kind",1)}},cn=class{constructor(r){_(this,"kind",2);this.ruleName=r}},mn=class{constructor(r){_(this,"kind",3);this.scopeName=r}},pn=class{constructor(r,e){_(this,"kind",4);this.scopeName=r,this.ruleName=e}};function Wt(r){if(r==="$base")return new ln;if(r==="$self")return new un;const e=r.indexOf("#");if(e===-1)return new mn(r);if(e===0)return new cn(r.substring(1));{const t=r.substring(0,e),n=r.substring(e+1);return new pn(t,n)}}var dn=/\\(\d+)/,pt=/\\(\d+)/g,hn=-1,Ht=-2;var ce=class{constructor(r,e,t,n){_(this,"$location");_(this,"id");_(this,"_nameIsCapturing");_(this,"_name");_(this,"_contentNameIsCapturing");_(this,"_contentName");this.$location=r,this.id=e,this._name=t||null,this._nameIsCapturing=he.hasCaptures(this._name),this._contentName=n||null,this._contentNameIsCapturing=he.hasCaptures(this._contentName)}get debugName(){const r=this.$location?`${xt(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${r}`}getName(r,e){return!this._nameIsCapturing||this._name===null||r===null||e===null?this._name:he.replaceCaptures(this._name,r,e)}getContentName(r,e){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:he.replaceCaptures(this._contentName,r,e)}},fn=class extends ce{constructor(e,t,n,i,o){super(e,t,n,i);_(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=o}dispose(){}collectPatterns(e,t){throw new Error("Not supported!")}compile(e,t){throw new Error("Not supported!")}compileAG(e,t,n,i){throw new Error("Not supported!")}},_n=class extends ce{constructor(e,t,n,i,o){super(e,t,n,null);_(this,"_match");_(this,"captures");_(this,"_cachedCompiledPatterns");this._match=new ae(i,this.id),this.captures=o,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new se,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},dt=class extends ce{constructor(e,t,n,i,o){super(e,t,n,i);_(this,"hasMissingPatterns");_(this,"patterns");_(this,"_cachedCompiledPatterns");this.patterns=o.patterns,this.hasMissingPatterns=o.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(const n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new se,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Fe=class extends ce{constructor(e,t,n,i,o,a,l,s,c,m){super(e,t,n,i);_(this,"_begin");_(this,"beginCaptures");_(this,"_end");_(this,"endHasBackReferences");_(this,"endCaptures");_(this,"applyEndPatternLast");_(this,"hasMissingPatterns");_(this,"patterns");_(this,"_cachedCompiledPatterns");this._begin=new ae(o,this.id),this.beginCaptures=a,this._end=new ae(l||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=s,this.applyEndPatternLast=c||!1,this.patterns=m.patterns,this.hasMissingPatterns=m.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,i)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new se;for(const n of this.patterns)e.getRule(n).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},Ae=class extends ce{constructor(e,t,n,i,o,a,l,s,c){super(e,t,n,i);_(this,"_begin");_(this,"beginCaptures");_(this,"whileCaptures");_(this,"_while");_(this,"whileHasBackReferences");_(this,"hasMissingPatterns");_(this,"patterns");_(this,"_cachedCompiledPatterns");_(this,"_cachedCompiledWhilePatterns");this._begin=new ae(o,this.id),this.beginCaptures=a,this.whileCaptures=s,this._while=new ae(l,Ht),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=c.patterns,this.hasMissingPatterns=c.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new se;for(const t of this.patterns)e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,i){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,i)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new se,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},Ft=class k{static createCaptureRule(e,t,n,i,o){return e.registerRule(a=>new fn(t,a,n,i,o))}static getCompiledRuleId(e,t,n){return e.id||t.registerRule(i=>{if(e.id=i,e.match)return new _n(e.$vscodeTextmateLocation,e.id,e.name,e.match,k._compileCaptures(e.captures,t,n));if(typeof e.begin>"u"){e.repository&&(n=Nt({},n,e.repository));let o=e.patterns;return typeof o>"u"&&e.include&&(o=[{include:e.include}]),new dt(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,k._compilePatterns(o,t,n))}return e.while?new Ae(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,k._compileCaptures(e.beginCaptures||e.captures,t,n),e.while,k._compileCaptures(e.whileCaptures||e.captures,t,n),k._compilePatterns(e.patterns,t,n)):new Fe(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,k._compileCaptures(e.beginCaptures||e.captures,t,n),e.end,k._compileCaptures(e.endCaptures||e.captures,t,n),e.applyEndPatternLast,k._compilePatterns(e.patterns,t,n))}),e.id}static _compileCaptures(e,t,n){let i=[];if(e){let o=0;for(const a in e){if(a==="$vscodeTextmateLocation")continue;const l=parseInt(a,10);l>o&&(o=l)}for(let a=0;a<=o;a++)i[a]=null;for(const a in e){if(a==="$vscodeTextmateLocation")continue;const l=parseInt(a,10);let s=0;e[a].patterns&&(s=k.getCompiledRuleId(e[a],t,n)),i[l]=k.createCaptureRule(t,e[a].$vscodeTextmateLocation,e[a].name,e[a].contentName,s)}}return i}static _compilePatterns(e,t,n){let i=[];if(e)for(let o=0,a=e.length;o<a;o++){const l=e[o];let s=-1;if(l.include){const c=Wt(l.include);switch(c.kind){case 0:case 1:s=k.getCompiledRuleId(n[l.include],t,n);break;case 2:let m=n[c.ruleName];m&&(s=k.getCompiledRuleId(m,t,n));break;case 3:case 4:const d=c.scopeName,h=c.kind===4?c.ruleName:null,p=t.getExternalGrammar(d,n);if(p)if(h){let f=p.repository[h];f&&(s=k.getCompiledRuleId(f,t,p.repository))}else s=k.getCompiledRuleId(p.repository.$self,t,p.repository);break}}else s=k.getCompiledRuleId(l,t,n);if(s!==-1){const c=t.getRule(s);let m=!1;if((c instanceof dt||c instanceof Fe||c instanceof Ae)&&c.hasMissingPatterns&&c.patterns.length===0&&(m=!0),m)continue;i.push(s)}}return{patterns:i,hasMissingPatterns:(e?e.length:0)!==i.length}}},ae=class zt{constructor(e,t){_(this,"source");_(this,"ruleId");_(this,"hasAnchor");_(this,"hasBackReferences");_(this,"_anchorCache");if(e&&typeof e=="string"){const n=e.length;let i=0,o=[],a=!1;for(let l=0;l<n;l++)if(e.charAt(l)==="\\"&&l+1<n){const c=e.charAt(l+1);c==="z"?(o.push(e.substring(i,l)),o.push("$(?!\\n)(?<!\\n)"),i=l+2):(c==="A"||c==="G")&&(a=!0),l++}this.hasAnchor=a,i===0?this.source=e:(o.push(e.substring(i,n)),this.source=o.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,typeof this.source=="string"?this.hasBackReferences=dn.test(this.source):this.hasBackReferences=!1}clone(){return new zt(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let n=t.map(i=>e.substring(i.start,i.end));return pt.lastIndex=0,this.source.replace(pt,(i,o)=>Bt(n[parseInt(o,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let e=[],t=[],n=[],i=[],o,a,l,s;for(o=0,a=this.source.length;o<a;o++)l=this.source.charAt(o),e[o]=l,t[o]=l,n[o]=l,i[o]=l,l==="\\"&&o+1<a&&(s=this.source.charAt(o+1),s==="A"?(e[o+1]="￿",t[o+1]="￿",n[o+1]="A",i[o+1]="A"):s==="G"?(e[o+1]="￿",t[o+1]="G",n[o+1]="￿",i[o+1]="G"):(e[o+1]=s,t[o+1]=s,n[o+1]=s,i[o+1]=s),o++);return{A0_G0:e.join(""),A0_G1:t.join(""),A1_G0:n.join(""),A1_G1:i.join("")}}resolveAnchors(e,t){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:e?t?this._anchorCache.A1_G1:this._anchorCache.A1_G0:t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},se=class{constructor(){_(this,"_items");_(this,"_hasAnchors");_(this,"_cached");_(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(r){this._items.push(r),this._hasAnchors=this._hasAnchors||r.hasAnchor}unshift(r){this._items.unshift(r),this._hasAnchors=this._hasAnchors||r.hasAnchor}length(){return this._items.length}setSource(r,e){this._items[r].source!==e&&(this._disposeCaches(),this._items[r].setSource(e))}compile(r){if(!this._cached){let e=this._items.map(t=>t.source);this._cached=new ht(r,e,this._items.map(t=>t.ruleId))}return this._cached}compileAG(r,e,t){return this._hasAnchors?e?t?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(r,e,t)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(r,e,t)),this._anchorCache.A1_G0):t?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(r,e,t)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(r,e,t)),this._anchorCache.A0_G0):this.compile(r)}_resolveAnchors(r,e,t){let n=this._items.map(i=>i.resolveAnchors(e,t));return new ht(r,n,this._items.map(i=>i.ruleId))}},ht=class{constructor(r,e,t){_(this,"scanner");this.regExps=e,this.rules=t,this.scanner=r.createOnigScanner(e)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const r=[];for(let e=0,t=this.rules.length;e<t;e++)r.push("   - "+this.rules[e]+": "+this.regExps[e]);return r.join(`
`)}findNextMatchSync(r,e,t){const n=this.scanner.findNextMatchSync(r,e,t);return n?{ruleId:this.rules[n.index],captureIndices:n.captureIndices}:null}},Me=class{constructor(r,e){this.languageId=r,this.tokenType=e}},W,gn=(W=class{constructor(e,t){_(this,"_defaultAttributes");_(this,"_embeddedLanguagesMatcher");_(this,"_getBasicScopeAttributes",new Gt(e=>{const t=this._scopeToLanguage(e),n=this._toStandardTokenType(e);return new Me(t,n)}));this._defaultAttributes=new Me(e,8),this._embeddedLanguagesMatcher=new yn(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(e){return e===null?W._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(e)}_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(e){const t=e.match(W.STANDARD_TOKEN_TYPE_REGEXP);if(!t)return 8;switch(t[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},_(W,"_NULL_SCOPE_METADATA",new Me(0,0)),_(W,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),W),yn=class{constructor(r){_(this,"values");_(this,"scopesRegExp");if(r.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(r);const e=r.map(([t,n])=>Bt(t));e.sort(),e.reverse(),this.scopesRegExp=new RegExp(`^((${e.join(")|(")}))($|\\.)`,"")}}match(r){if(!this.scopesRegExp)return;const e=r.match(this.scopesRegExp);if(e)return this.values.get(e[1])}},ft=class{constructor(r,e){this.stack=r,this.stoppedEarly=e}};function qt(r,e,t,n,i,o,a,l){const s=e.content.length;let c=!1,m=-1;if(a){const p=En(r,e,t,n,i,o);i=p.stack,n=p.linePos,t=p.isFirstLine,m=p.anchorPosition}const d=Date.now();for(;!c;){if(l!==0&&Date.now()-d>l)return new ft(i,!0);h()}return new ft(i,!1);function h(){const p=vn(r,e,t,n,i,m);if(!p){o.produce(i,s),c=!0;return}const f=p.captureIndices,R=p.matchedRuleId,E=f&&f.length>0?f[0].end>n:!1;if(R===hn){const y=i.getRule(r);o.produce(i,f[0].start),i=i.withContentNameScopesList(i.nameScopesList),ne(r,e,t,i,o,y.endCaptures,f),o.produce(i,f[0].end);const v=i;if(i=i.parent,m=v.getAnchorPos(),!E&&v.getEnterPos()===n){i=v,o.produce(i,s),c=!0;return}}else{const y=r.getRule(R);o.produce(i,f[0].start);const v=i,A=y.getName(e.content,f),P=i.contentNameScopesList.pushAttributed(A,r);if(i=i.push(R,n,m,f[0].end===s,null,P,P),y instanceof Fe){const L=y;ne(r,e,t,i,o,L.beginCaptures,f),o.produce(i,f[0].end),m=f[0].end;const N=L.getContentName(e.content,f),I=P.pushAttributed(N,r);if(i=i.withContentNameScopesList(I),L.endHasBackReferences&&(i=i.withEndRule(L.getEndWithResolvedBackReferences(e.content,f))),!E&&v.hasSameRuleAs(i)){i=i.pop(),o.produce(i,s),c=!0;return}}else if(y instanceof Ae){const L=y;ne(r,e,t,i,o,L.beginCaptures,f),o.produce(i,f[0].end),m=f[0].end;const N=L.getContentName(e.content,f),I=P.pushAttributed(N,r);if(i=i.withContentNameScopesList(I),L.whileHasBackReferences&&(i=i.withEndRule(L.getWhileWithResolvedBackReferences(e.content,f))),!E&&v.hasSameRuleAs(i)){i=i.pop(),o.produce(i,s),c=!0;return}}else if(ne(r,e,t,i,o,y.captures,f),o.produce(i,f[0].end),i=i.pop(),!E){i=i.safePop(),o.produce(i,s),c=!0;return}}f[0].end>n&&(n=f[0].end,t=!1)}}function En(r,e,t,n,i,o){let a=i.beginRuleCapturedEOL?0:-1;const l=[];for(let s=i;s;s=s.pop()){const c=s.getRule(r);c instanceof Ae&&l.push({rule:c,stack:s})}for(let s=l.pop();s;s=l.pop()){const{ruleScanner:c,findOptions:m}=bn(s.rule,r,s.stack.endRule,t,n===a),d=c.findNextMatchSync(e,n,m);if(d){if(d.ruleId!==Ht){i=s.stack.pop();break}d.captureIndices&&d.captureIndices.length&&(o.produce(s.stack,d.captureIndices[0].start),ne(r,e,t,s.stack,o,s.rule.whileCaptures,d.captureIndices),o.produce(s.stack,d.captureIndices[0].end),a=d.captureIndices[0].end,d.captureIndices[0].end>n&&(n=d.captureIndices[0].end,t=!1))}else{i=s.stack.pop();break}}return{stack:i,linePos:n,anchorPosition:a,isFirstLine:t}}function vn(r,e,t,n,i,o){const a=Rn(r,e,t,n,i,o),l=r.getInjections();if(l.length===0)return a;const s=An(l,r,e,t,n,i,o);if(!s)return a;if(!a)return s;const c=a.captureIndices[0].start,m=s.captureIndices[0].start;return m<c||s.priorityMatch&&m===c?s:a}function Rn(r,e,t,n,i,o){const a=i.getRule(r),{ruleScanner:l,findOptions:s}=Kt(a,r,i.endRule,t,n===o),c=l.findNextMatchSync(e,n,s);return c?{captureIndices:c.captureIndices,matchedRuleId:c.ruleId}:null}function An(r,e,t,n,i,o,a){let l=Number.MAX_VALUE,s=null,c,m=0;const d=o.contentNameScopesList.getScopeNames();for(let h=0,p=r.length;h<p;h++){const f=r[h];if(!f.matcher(d))continue;const R=e.getRule(f.ruleId),{ruleScanner:E,findOptions:y}=Kt(R,e,null,n,i===a),v=E.findNextMatchSync(t,i,y);if(!v)continue;const A=v.captureIndices[0].start;if(!(A>=l)&&(l=A,s=v.captureIndices,c=v.ruleId,m=f.priority,l===i))break}return s?{priorityMatch:m===-1,captureIndices:s,matchedRuleId:c}:null}function Kt(r,e,t,n,i){return{ruleScanner:r.compileAG(e,t,n,i),findOptions:0}}function bn(r,e,t,n,i){return{ruleScanner:r.compileWhileAG(e,t,n,i),findOptions:0}}function ne(r,e,t,n,i,o,a){if(o.length===0)return;const l=e.content,s=Math.min(o.length,a.length),c=[],m=a[0].end;for(let d=0;d<s;d++){const h=o[d];if(h===null)continue;const p=a[d];if(p.length===0)continue;if(p.start>m)break;for(;c.length>0&&c[c.length-1].endPos<=p.start;)i.produceFromScopes(c[c.length-1].scopes,c[c.length-1].endPos),c.pop();if(c.length>0?i.produceFromScopes(c[c.length-1].scopes,p.start):i.produce(n,p.start),h.retokenizeCapturedWithRuleId){const R=h.getName(l,a),E=n.contentNameScopesList.pushAttributed(R,r),y=h.getContentName(l,a),v=E.pushAttributed(y,r),A=n.push(h.retokenizeCapturedWithRuleId,p.start,-1,!1,null,E,v),P=r.createOnigString(l.substring(0,p.end));qt(r,P,t&&p.start===0,p.start,A,i,!1,0),$t(P);continue}const f=h.getName(l,a);if(f!==null){const E=(c.length>0?c[c.length-1].scopes:n.contentNameScopesList).pushAttributed(f,r);c.push(new Pn(E,p.end))}}for(;c.length>0;)i.produceFromScopes(c[c.length-1].scopes,c[c.length-1].endPos),c.pop()}var Pn=class{constructor(r,e){_(this,"scopes");_(this,"endPos");this.scopes=r,this.endPos=e}};function Tn(r,e,t,n,i,o,a,l){return new Sn(r,e,t,n,i,o,a,l)}function _t(r,e,t,n,i){const o=ve(e,be),a=Ft.getCompiledRuleId(t,n,i.repository);for(const l of o)r.push({debugSelector:e,matcher:l.matcher,ruleId:a,grammar:i,priority:l.priority})}function be(r,e){if(e.length<r.length)return!1;let t=0;return r.every(n=>{for(let i=t;i<e.length;i++)if(Ln(e[i],n))return t=i+1,!0;return!1})}function Ln(r,e){if(!r)return!1;if(r===e)return!0;const t=e.length;return r.length>t&&r.substr(0,t)===e&&r[t]==="."}var Sn=class{constructor(r,e,t,n,i,o,a,l){_(this,"_rootId");_(this,"_lastRuleId");_(this,"_ruleId2desc");_(this,"_includedGrammars");_(this,"_grammarRepository");_(this,"_grammar");_(this,"_injections");_(this,"_basicScopeAttributesProvider");_(this,"_tokenTypeMatchers");if(this._rootScopeName=r,this.balancedBracketSelectors=o,this._onigLib=l,this._basicScopeAttributesProvider=new gn(t,n),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=a,this._grammar=gt(e,null),this._injections=null,this._tokenTypeMatchers=[],i)for(const s of Object.keys(i)){const c=ve(s,be);for(const m of c)this._tokenTypeMatchers.push({matcher:m.matcher,type:i[s]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const r of this._ruleId2desc)r&&r.dispose()}createOnigScanner(r){return this._onigLib.createOnigScanner(r)}createOnigString(r){return this._onigLib.createOnigString(r)}getMetadataForScope(r){return this._basicScopeAttributesProvider.getBasicScopeAttributes(r)}_collectInjections(){const r={lookup:i=>i===this._rootScopeName?this._grammar:this.getExternalGrammar(i),injections:i=>this._grammarRepository.injections(i)},e=[],t=this._rootScopeName,n=r.lookup(t);if(n){const i=n.injections;if(i)for(let a in i)_t(e,a,i[a],this,n);const o=this._grammarRepository.injections(t);o&&o.forEach(a=>{const l=this.getExternalGrammar(a);if(l){const s=l.injectionSelector;s&&_t(e,s,l,this,l)}})}return e.sort((i,o)=>i.priority-o.priority),e}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(r){const e=++this._lastRuleId,t=r(e);return this._ruleId2desc[e]=t,t}getRule(r){return this._ruleId2desc[r]}getExternalGrammar(r,e){if(this._includedGrammars[r])return this._includedGrammars[r];if(this._grammarRepository){const t=this._grammarRepository.lookup(r);if(t)return this._includedGrammars[r]=gt(t,e&&e.$base),this._includedGrammars[r]}}tokenizeLine(r,e,t=0){const n=this._tokenize(r,e,!1,t);return{tokens:n.lineTokens.getResult(n.ruleStack,n.lineLength),ruleStack:n.ruleStack,stoppedEarly:n.stoppedEarly}}tokenizeLine2(r,e,t=0){const n=this._tokenize(r,e,!0,t);return{tokens:n.lineTokens.getBinaryResult(n.ruleStack,n.lineLength),ruleStack:n.ruleStack,stoppedEarly:n.stoppedEarly}}_tokenize(r,e,t,n){this._rootId===-1&&(this._rootId=Ft.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let i;if(!e||e===ze.NULL){i=!0;const c=this._basicScopeAttributesProvider.getDefaultAttributes(),m=this.themeProvider.getDefaults(),d=Q.set(0,c.languageId,c.tokenType,null,m.fontStyle,m.foregroundId,m.backgroundId),h=this.getRule(this._rootId).getName(null,null);let p;h?p=ie.createRootAndLookUpScopeName(h,d,this):p=ie.createRoot("unknown",d),e=new ze(null,this._rootId,-1,-1,!1,null,p,p)}else i=!1,e.reset();r=r+`
`;const o=this.createOnigString(r),a=o.content.length,l=new On(t,r,this._tokenTypeMatchers,this.balancedBracketSelectors),s=qt(this,o,i,0,e,l,!0,n);return $t(o),{lineLength:a,lineTokens:l,ruleStack:s.stack,stoppedEarly:s.stoppedEarly}}};function gt(r,e){return r=Fr(r),r.repository=r.repository||{},r.repository.$self={$vscodeTextmateLocation:r.$vscodeTextmateLocation,patterns:r.patterns,name:r.scopeName},r.repository.$base=e||r.repository.$self,r}var ie=class j{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(e,t){let n=e,i=(e==null?void 0:e.scopePath)??null;for(const o of t)i=Ve.push(i,o.scopeNames),n=new j(n,i,o.encodedTokenAttributes);return n}static createRoot(e,t){return new j(null,new Ve(null,e),t)}static createRootAndLookUpScopeName(e,t,n){const i=n.getMetadataForScope(e),o=new Ve(null,e),a=n.themeProvider.themeMatch(o),l=j.mergeAttributes(t,i,a);return new j(null,o,l)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(e){return j.equals(this,e)}static equals(e,t){do{if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}while(!0)}static mergeAttributes(e,t,n){let i=-1,o=0,a=0;return n!==null&&(i=n.fontStyle,o=n.foregroundId,a=n.backgroundId),Q.set(e,t.languageId,t.tokenType,null,i,o,a)}pushAttributed(e,t){if(e===null)return this;if(e.indexOf(" ")===-1)return j._pushAttributed(this,e,t);const n=e.split(/ /g);let i=this;for(const o of n)i=j._pushAttributed(i,o,t);return i}static _pushAttributed(e,t,n){const i=n.getMetadataForScope(t),o=e.scopePath.push(t),a=n.themeProvider.themeMatch(o),l=j.mergeAttributes(e.tokenAttributes,i,a);return new j(e,o,l)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){var i;const t=[];let n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(((i=n.parent)==null?void 0:i.scopePath)??null)}),n=n.parent;return n===e?t.reverse():void 0}},B,ze=(B=class{constructor(e,t,n,i,o,a,l,s){_(this,"_stackElementBrand");_(this,"_enterPos");_(this,"_anchorPos");_(this,"depth");this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=o,this.endRule=a,this.nameScopesList=l,this.contentNameScopesList=s,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=i}equals(e){return e===null?!1:B._equals(this,e)}static _equals(e,t){return e===t?!0:this._structuralEquals(e,t)?ie.equals(e.contentNameScopesList,t.contentNameScopesList):!1}static _structuralEquals(e,t){do{if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}while(!0)}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){B._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(e,t,n,i,o,a,l){return new B(this,e,t,n,i,o,a,l)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){const e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){var n,i;return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${(n=this.nameScopesList)==null?void 0:n.toString()}, ${(i=this.contentNameScopesList)==null?void 0:i.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(e){return this.endRule===e?this:new B(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,e,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){var e,t,n;return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:((t=this.nameScopesList)==null?void 0:t.getExtensionIfDefined(((e=this.parent)==null?void 0:e.nameScopesList)??null))??[],contentNameScopesList:((n=this.contentNameScopesList)==null?void 0:n.getExtensionIfDefined(this.nameScopesList))??[]}}static pushFrame(e,t){const n=ie.fromExtension((e==null?void 0:e.nameScopesList)??null,t.nameScopesList);return new B(e,t.ruleId,t.enterPos??-1,t.anchorPos??-1,t.beginRuleCapturedEOL,t.endRule,n,ie.fromExtension(n,t.contentNameScopesList))}},_(B,"NULL",new B(null,0,0,0,!1,null,null,null)),B),In=class{constructor(r,e){_(this,"balancedBracketScopes");_(this,"unbalancedBracketScopes");_(this,"allowAny",!1);this.balancedBracketScopes=r.flatMap(t=>t==="*"?(this.allowAny=!0,[]):ve(t,be).map(n=>n.matcher)),this.unbalancedBracketScopes=e.flatMap(t=>ve(t,be).map(n=>n.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(r){for(const e of this.unbalancedBracketScopes)if(e(r))return!1;for(const e of this.balancedBracketScopes)if(e(r))return!0;return this.allowAny}},On=class{constructor(r,e,t,n){_(this,"_emitBinaryTokens");_(this,"_lineText");_(this,"_tokens");_(this,"_binaryTokens");_(this,"_lastTokenEndIndex");_(this,"_tokenTypeOverrides");this.balancedBracketSelectors=n,this._emitBinaryTokens=r,this._tokenTypeOverrides=t,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(r,e){this.produceFromScopes(r.contentNameScopesList,e)}produceFromScopes(r,e){var n;if(this._lastTokenEndIndex>=e)return;if(this._emitBinaryTokens){let i=(r==null?void 0:r.tokenAttributes)??0,o=!1;if((n=this.balancedBracketSelectors)!=null&&n.matchesAlways&&(o=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const a=(r==null?void 0:r.getScopeNames())??[];for(const l of this._tokenTypeOverrides)l.matcher(a)&&(i=Q.set(i,0,l.type,null,-1,0,0));this.balancedBracketSelectors&&(o=this.balancedBracketSelectors.match(a))}if(o&&(i=Q.set(i,0,8,o,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===i){this._lastTokenEndIndex=e;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(i),this._lastTokenEndIndex=e;return}const t=(r==null?void 0:r.getScopeNames())??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:e,scopes:t}),this._lastTokenEndIndex=e}getResult(r,e){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===e-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(r,e),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(r,e){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===e-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(r,e),this._binaryTokens[this._binaryTokens.length-2]=0);const t=new Uint32Array(this._binaryTokens.length);for(let n=0,i=this._binaryTokens.length;n<i;n++)t[n]=this._binaryTokens[n];return t}},Cn=class{constructor(r,e){_(this,"_grammars",new Map);_(this,"_rawGrammars",new Map);_(this,"_injectionGrammars",new Map);_(this,"_theme");this._onigLib=e,this._theme=r}dispose(){for(const r of this._grammars.values())r.dispose()}setTheme(r){this._theme=r}getColorMap(){return this._theme.getColorMap()}addGrammar(r,e){this._rawGrammars.set(r.scopeName,r),e&&this._injectionGrammars.set(r.scopeName,e)}lookup(r){return this._rawGrammars.get(r)}injections(r){return this._injectionGrammars.get(r)}getDefaults(){return this._theme.getDefaults()}themeMatch(r){return this._theme.match(r)}grammarForScopeName(r,e,t,n,i){if(!this._grammars.has(r)){let o=this._rawGrammars.get(r);if(!o)return null;this._grammars.set(r,Tn(r,o,e,t,n,i,this,this._onigLib))}return this._grammars.get(r)}},wn=class{constructor(e){_(this,"_options");_(this,"_syncRegistry");_(this,"_ensureGrammarCache");this._options=e,this._syncRegistry=new Cn(Ee.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(Ee.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new In(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,n,i,o){const a=new an(this._syncRegistry,e);for(;a.Q.length>0;)a.Q.map(l=>this._loadSingleGrammar(l.scopeName)),a.processQueue();return this._grammarForScopeName(e,t,n,i,o)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){const t=this._options.loadGrammar(e);if(t){const n=typeof this._options.getInjections=="function"?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}addGrammar(e,t=[],n=0,i=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,n,i)}_grammarForScopeName(e,t=0,n=null,i=null,o=null){return this._syncRegistry.grammarForScopeName(e,t,n,i,o)}},qe=ze.NULL;const kn=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class me{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}me.prototype.normal={};me.prototype.property={};me.prototype.space=void 0;function Jt(r,e){const t={},n={};for(const i of r)Object.assign(t,i.property),Object.assign(n,i.normal);return new me(t,n,e)}function Ke(r){return r.toLowerCase()}class x{constructor(e,t){this.attribute=t,this.property=e}}x.prototype.attribute="";x.prototype.booleanish=!1;x.prototype.boolean=!1;x.prototype.commaOrSpaceSeparated=!1;x.prototype.commaSeparated=!1;x.prototype.defined=!1;x.prototype.mustUseProperty=!1;x.prototype.number=!1;x.prototype.overloadedBoolean=!1;x.prototype.property="";x.prototype.spaceSeparated=!1;x.prototype.space=void 0;let Dn=0;const b=K(),S=K(),Je=K(),g=K(),T=K(),X=K(),V=K();function K(){return 2**++Dn}const Xe=Object.freeze(Object.defineProperty({__proto__:null,boolean:b,booleanish:S,commaOrSpaceSeparated:V,commaSeparated:X,number:g,overloadedBoolean:Je,spaceSeparated:T},Symbol.toStringTag,{value:"Module"})),Be=Object.keys(Xe);class et extends x{constructor(e,t,n,i){let o=-1;if(super(e,t),yt(this,"space",i),typeof n=="number")for(;++o<Be.length;){const a=Be[o];yt(this,Be[o],(n&Xe[a])===Xe[a])}}}et.prototype.defined=!0;function yt(r,e,t){t&&(r[e]=t)}function Z(r){const e={},t={};for(const[n,i]of Object.entries(r.properties)){const o=new et(n,r.transform(r.attributes||{},n),i,r.space);r.mustUseProperty&&r.mustUseProperty.includes(n)&&(o.mustUseProperty=!0),e[n]=o,t[Ke(n)]=n,t[Ke(o.attribute)]=n}return new me(e,t,r.space)}const Xt=Z({properties:{ariaActiveDescendant:null,ariaAtomic:S,ariaAutoComplete:null,ariaBusy:S,ariaChecked:S,ariaColCount:g,ariaColIndex:g,ariaColSpan:g,ariaControls:T,ariaCurrent:null,ariaDescribedBy:T,ariaDetails:null,ariaDisabled:S,ariaDropEffect:T,ariaErrorMessage:null,ariaExpanded:S,ariaFlowTo:T,ariaGrabbed:S,ariaHasPopup:null,ariaHidden:S,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:T,ariaLevel:g,ariaLive:null,ariaModal:S,ariaMultiLine:S,ariaMultiSelectable:S,ariaOrientation:null,ariaOwns:T,ariaPlaceholder:null,ariaPosInSet:g,ariaPressed:S,ariaReadOnly:S,ariaRelevant:null,ariaRequired:S,ariaRoleDescription:T,ariaRowCount:g,ariaRowIndex:g,ariaRowSpan:g,ariaSelected:S,ariaSetSize:g,ariaSort:null,ariaValueMax:g,ariaValueMin:g,ariaValueNow:g,ariaValueText:null,role:null},transform(r,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function Yt(r,e){return e in r?r[e]:e}function Qt(r,e){return Yt(r,e.toLowerCase())}const Nn=Z({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:X,acceptCharset:T,accessKey:T,action:null,allow:null,allowFullScreen:b,allowPaymentRequest:b,allowUserMedia:b,alt:null,as:null,async:b,autoCapitalize:null,autoComplete:T,autoFocus:b,autoPlay:b,blocking:T,capture:null,charSet:null,checked:b,cite:null,className:T,cols:g,colSpan:null,content:null,contentEditable:S,controls:b,controlsList:T,coords:g|X,crossOrigin:null,data:null,dateTime:null,decoding:null,default:b,defer:b,dir:null,dirName:null,disabled:b,download:Je,draggable:S,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:b,formTarget:null,headers:T,height:g,hidden:Je,high:g,href:null,hrefLang:null,htmlFor:T,httpEquiv:T,id:null,imageSizes:null,imageSrcSet:null,inert:b,inputMode:null,integrity:null,is:null,isMap:b,itemId:null,itemProp:T,itemRef:T,itemScope:b,itemType:T,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:b,low:g,manifest:null,max:null,maxLength:g,media:null,method:null,min:null,minLength:g,multiple:b,muted:b,name:null,nonce:null,noModule:b,noValidate:b,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:b,optimum:g,pattern:null,ping:T,placeholder:null,playsInline:b,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:b,referrerPolicy:null,rel:T,required:b,reversed:b,rows:g,rowSpan:g,sandbox:T,scope:null,scoped:b,seamless:b,selected:b,shadowRootClonable:b,shadowRootDelegatesFocus:b,shadowRootMode:null,shape:null,size:g,sizes:null,slot:null,span:g,spellCheck:S,src:null,srcDoc:null,srcLang:null,srcSet:null,start:g,step:null,style:null,tabIndex:g,target:null,title:null,translate:null,type:null,typeMustMatch:b,useMap:null,value:S,width:g,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:T,axis:null,background:null,bgColor:null,border:g,borderColor:null,bottomMargin:g,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:b,declare:b,event:null,face:null,frame:null,frameBorder:null,hSpace:g,leftMargin:g,link:null,longDesc:null,lowSrc:null,marginHeight:g,marginWidth:g,noResize:b,noHref:b,noShade:b,noWrap:b,object:null,profile:null,prompt:null,rev:null,rightMargin:g,rules:null,scheme:null,scrolling:S,standby:null,summary:null,text:null,topMargin:g,valueType:null,version:null,vAlign:null,vLink:null,vSpace:g,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:b,disableRemotePlayback:b,prefix:null,property:null,results:g,security:null,unselectable:null},space:"html",transform:Qt}),xn=Z({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:V,accentHeight:g,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:g,amplitude:g,arabicForm:null,ascent:g,attributeName:null,attributeType:null,azimuth:g,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:g,by:null,calcMode:null,capHeight:g,className:T,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:g,diffuseConstant:g,direction:null,display:null,dur:null,divisor:g,dominantBaseline:null,download:b,dx:null,dy:null,edgeMode:null,editable:null,elevation:g,enableBackground:null,end:null,event:null,exponent:g,externalResourcesRequired:null,fill:null,fillOpacity:g,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:X,g2:X,glyphName:X,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:g,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:g,horizOriginX:g,horizOriginY:g,id:null,ideographic:g,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:g,k:g,k1:g,k2:g,k3:g,k4:g,kernelMatrix:V,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:g,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:g,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:g,overlineThickness:g,paintOrder:null,panose1:null,path:null,pathLength:g,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:T,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:g,pointsAtY:g,pointsAtZ:g,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:V,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:V,rev:V,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:V,requiredFeatures:V,requiredFonts:V,requiredFormats:V,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:g,specularExponent:g,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:g,strikethroughThickness:g,string:null,stroke:null,strokeDashArray:V,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:g,strokeOpacity:g,strokeWidth:null,style:null,surfaceScale:g,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:V,tabIndex:g,tableValues:null,target:null,targetX:g,targetY:g,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:V,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:g,underlineThickness:g,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:g,values:null,vAlphabetic:g,vMathematical:g,vectorEffect:null,vHanging:g,vIdeographic:g,version:null,vertAdvY:g,vertOriginX:g,vertOriginY:g,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:g,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Yt}),Zt=Z({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(r,e){return"xlink:"+e.slice(5).toLowerCase()}}),er=Z({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Qt}),tr=Z({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(r,e){return"xml:"+e.slice(3).toLowerCase()}}),Vn=/[A-Z]/g,Et=/-[a-z]/g,Mn=/^data[-\w.:]+$/i;function Bn(r,e){const t=Ke(e);let n=e,i=x;if(t in r.normal)return r.property[r.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&Mn.test(e)){if(e.charAt(4)==="-"){const o=e.slice(5).replace(Et,jn);n="data"+o.charAt(0).toUpperCase()+o.slice(1)}else{const o=e.slice(4);if(!Et.test(o)){let a=o.replace(Vn,Gn);a.charAt(0)!=="-"&&(a="-"+a),e="data"+a}}i=et}return new i(n,e)}function Gn(r){return"-"+r.toLowerCase()}function jn(r){return r.charAt(1).toUpperCase()}const Un=Jt([Xt,Nn,Zt,er,tr],"html"),rr=Jt([Xt,xn,Zt,er,tr],"svg"),vt={}.hasOwnProperty;function $n(r,e){const t=e||{};function n(i,...o){let a=n.invalid;const l=n.handlers;if(i&&vt.call(i,r)){const s=String(i[r]);a=vt.call(l,s)?l[s]:n.unknown}if(a)return a.call(this,i,...o)}return n.handlers=t.handlers||{},n.invalid=t.invalid,n.unknown=t.unknown,n}const Wn=/["&'<>`]/g,Hn=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Fn=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,zn=/[|\\{}()[\]^$+*?.]/g,Rt=new WeakMap;function qn(r,e){if(r=r.replace(e.subset?Kn(e.subset):Wn,n),e.subset||e.escapeOnly)return r;return r.replace(Hn,t).replace(Fn,n);function t(i,o,a){return e.format((i.charCodeAt(0)-55296)*1024+i.charCodeAt(1)-56320+65536,a.charCodeAt(o+2),e)}function n(i,o,a){return e.format(i.charCodeAt(0),a.charCodeAt(o+1),e)}}function Kn(r){let e=Rt.get(r);return e||(e=Jn(r),Rt.set(r,e)),e}function Jn(r){const e=[];let t=-1;for(;++t<r.length;)e.push(r[t].replace(zn,"\\$&"));return new RegExp("(?:"+e.join("|")+")","g")}const Xn=/[\dA-Fa-f]/;function Yn(r,e,t){const n="&#x"+r.toString(16).toUpperCase();return t&&e&&!Xn.test(String.fromCharCode(e))?n:n+";"}const Qn=/\d/;function Zn(r,e,t){const n="&#"+String(r);return t&&e&&!Qn.test(String.fromCharCode(e))?n:n+";"}const ei=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],Ge={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},ti=["cent","copy","divide","gt","lt","not","para","times"],nr={}.hasOwnProperty,Ye={};let fe;for(fe in Ge)nr.call(Ge,fe)&&(Ye[Ge[fe]]=fe);const ri=/[^\dA-Za-z]/;function ni(r,e,t,n){const i=String.fromCharCode(r);if(nr.call(Ye,i)){const o=Ye[i],a="&"+o;return t&&ei.includes(o)&&!ti.includes(o)&&(!n||e&&e!==61&&ri.test(String.fromCharCode(e)))?a:a+";"}return""}function ii(r,e,t){let n=Yn(r,e,t.omitOptionalSemicolons),i;if((t.useNamedReferences||t.useShortestReferences)&&(i=ni(r,e,t.omitOptionalSemicolons,t.attribute)),(t.useShortestReferences||!i)&&t.useShortestReferences){const o=Zn(r,e,t.omitOptionalSemicolons);o.length<n.length&&(n=o)}return i&&(!t.useShortestReferences||i.length<n.length)?i:n}function Y(r,e){return qn(r,Object.assign({format:ii},e))}const oi=/^>|^->|<!--|-->|--!>|<!-$/g,ai=[">"],si=["<",">"];function li(r,e,t,n){return n.settings.bogusComments?"<?"+Y(r.value,Object.assign({},n.settings.characterReferences,{subset:ai}))+">":"<!--"+r.value.replace(oi,i)+"-->";function i(o){return Y(o,Object.assign({},n.settings.characterReferences,{subset:si}))}}function ui(r,e,t,n){return"<!"+(n.settings.upperDoctype?"DOCTYPE":"doctype")+(n.settings.tightDoctype?"":" ")+"html>"}function At(r,e){const t=String(r);if(typeof e!="string")throw new TypeError("Expected character");let n=0,i=t.indexOf(e);for(;i!==-1;)n++,i=t.indexOf(e,i+e.length);return n}function ci(r,e){const t=e||{};return(r[r.length-1]===""?[...r,""]:r).join((t.padRight?" ":"")+","+(t.padLeft===!1?"":" ")).trim()}function mi(r){return r.join(" ").trim()}const pi=/[ \t\n\f\r]/g;function tt(r){return typeof r=="object"?r.type==="text"?bt(r.value):!1:bt(r)}function bt(r){return r.replace(pi,"")===""}const C=or(1),ir=or(-1),di=[];function or(r){return e;function e(t,n,i){const o=t?t.children:di;let a=(n||0)+r,l=o[a];if(!i)for(;l&&tt(l);)a+=r,l=o[a];return l}}const hi={}.hasOwnProperty;function ar(r){return e;function e(t,n,i){return hi.call(r,t.tagName)&&r[t.tagName](t,n,i)}}const rt=ar({body:_i,caption:je,colgroup:je,dd:vi,dt:Ei,head:je,html:fi,li:yi,optgroup:Ri,option:Ai,p:gi,rp:Pt,rt:Pt,tbody:Pi,td:Tt,tfoot:Ti,th:Tt,thead:bi,tr:Li});function je(r,e,t){const n=C(t,e,!0);return!n||n.type!=="comment"&&!(n.type==="text"&&tt(n.value.charAt(0)))}function fi(r,e,t){const n=C(t,e);return!n||n.type!=="comment"}function _i(r,e,t){const n=C(t,e);return!n||n.type!=="comment"}function gi(r,e,t){const n=C(t,e);return n?n.type==="element"&&(n.tagName==="address"||n.tagName==="article"||n.tagName==="aside"||n.tagName==="blockquote"||n.tagName==="details"||n.tagName==="div"||n.tagName==="dl"||n.tagName==="fieldset"||n.tagName==="figcaption"||n.tagName==="figure"||n.tagName==="footer"||n.tagName==="form"||n.tagName==="h1"||n.tagName==="h2"||n.tagName==="h3"||n.tagName==="h4"||n.tagName==="h5"||n.tagName==="h6"||n.tagName==="header"||n.tagName==="hgroup"||n.tagName==="hr"||n.tagName==="main"||n.tagName==="menu"||n.tagName==="nav"||n.tagName==="ol"||n.tagName==="p"||n.tagName==="pre"||n.tagName==="section"||n.tagName==="table"||n.tagName==="ul"):!t||!(t.type==="element"&&(t.tagName==="a"||t.tagName==="audio"||t.tagName==="del"||t.tagName==="ins"||t.tagName==="map"||t.tagName==="noscript"||t.tagName==="video"))}function yi(r,e,t){const n=C(t,e);return!n||n.type==="element"&&n.tagName==="li"}function Ei(r,e,t){const n=C(t,e);return!!(n&&n.type==="element"&&(n.tagName==="dt"||n.tagName==="dd"))}function vi(r,e,t){const n=C(t,e);return!n||n.type==="element"&&(n.tagName==="dt"||n.tagName==="dd")}function Pt(r,e,t){const n=C(t,e);return!n||n.type==="element"&&(n.tagName==="rp"||n.tagName==="rt")}function Ri(r,e,t){const n=C(t,e);return!n||n.type==="element"&&n.tagName==="optgroup"}function Ai(r,e,t){const n=C(t,e);return!n||n.type==="element"&&(n.tagName==="option"||n.tagName==="optgroup")}function bi(r,e,t){const n=C(t,e);return!!(n&&n.type==="element"&&(n.tagName==="tbody"||n.tagName==="tfoot"))}function Pi(r,e,t){const n=C(t,e);return!n||n.type==="element"&&(n.tagName==="tbody"||n.tagName==="tfoot")}function Ti(r,e,t){return!C(t,e)}function Li(r,e,t){const n=C(t,e);return!n||n.type==="element"&&n.tagName==="tr"}function Tt(r,e,t){const n=C(t,e);return!n||n.type==="element"&&(n.tagName==="td"||n.tagName==="th")}const Si=ar({body:Ci,colgroup:wi,head:Oi,html:Ii,tbody:ki});function Ii(r){const e=C(r,-1);return!e||e.type!=="comment"}function Oi(r){const e=new Set;for(const n of r.children)if(n.type==="element"&&(n.tagName==="base"||n.tagName==="title")){if(e.has(n.tagName))return!1;e.add(n.tagName)}const t=r.children[0];return!t||t.type==="element"}function Ci(r){const e=C(r,-1,!0);return!e||e.type!=="comment"&&!(e.type==="text"&&tt(e.value.charAt(0)))&&!(e.type==="element"&&(e.tagName==="meta"||e.tagName==="link"||e.tagName==="script"||e.tagName==="style"||e.tagName==="template"))}function wi(r,e,t){const n=ir(t,e),i=C(r,-1,!0);return t&&n&&n.type==="element"&&n.tagName==="colgroup"&&rt(n,t.children.indexOf(n),t)?!1:!!(i&&i.type==="element"&&i.tagName==="col")}function ki(r,e,t){const n=ir(t,e),i=C(r,-1);return t&&n&&n.type==="element"&&(n.tagName==="thead"||n.tagName==="tbody")&&rt(n,t.children.indexOf(n),t)?!1:!!(i&&i.type==="element"&&i.tagName==="tr")}const _e={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function Di(r,e,t,n){const i=n.schema,o=i.space==="svg"?!1:n.settings.omitOptionalTags;let a=i.space==="svg"?n.settings.closeEmptyElements:n.settings.voids.includes(r.tagName.toLowerCase());const l=[];let s;i.space==="html"&&r.tagName==="svg"&&(n.schema=rr);const c=Ni(n,r.properties),m=n.all(i.space==="html"&&r.tagName==="template"?r.content:r);return n.schema=i,m&&(a=!1),(c||!o||!Si(r,e,t))&&(l.push("<",r.tagName,c?" "+c:""),a&&(i.space==="svg"||n.settings.closeSelfClosing)&&(s=c.charAt(c.length-1),(!n.settings.tightSelfClosing||s==="/"||s&&s!=='"'&&s!=="'")&&l.push(" "),l.push("/")),l.push(">")),l.push(m),!a&&(!o||!rt(r,e,t))&&l.push("</"+r.tagName+">"),l.join("")}function Ni(r,e){const t=[];let n=-1,i;if(e){for(i in e)if(e[i]!==null&&e[i]!==void 0){const o=xi(r,i,e[i]);o&&t.push(o)}}for(;++n<t.length;){const o=r.settings.tightAttributes?t[n].charAt(t[n].length-1):void 0;n!==t.length-1&&o!=='"'&&o!=="'"&&(t[n]+=" ")}return t.join("")}function xi(r,e,t){const n=Bn(r.schema,e),i=r.settings.allowParseErrors&&r.schema.space==="html"?0:1,o=r.settings.allowDangerousCharacters?0:1;let a=r.quote,l;if(n.overloadedBoolean&&(t===n.attribute||t==="")?t=!0:(n.boolean||n.overloadedBoolean)&&(typeof t!="string"||t===n.attribute||t==="")&&(t=!!t),t==null||t===!1||typeof t=="number"&&Number.isNaN(t))return"";const s=Y(n.attribute,Object.assign({},r.settings.characterReferences,{subset:_e.name[i][o]}));return t===!0||(t=Array.isArray(t)?(n.commaSeparated?ci:mi)(t,{padLeft:!r.settings.tightCommaSeparatedLists}):String(t),r.settings.collapseEmptyAttributes&&!t)?s:(r.settings.preferUnquoted&&(l=Y(t,Object.assign({},r.settings.characterReferences,{attribute:!0,subset:_e.unquoted[i][o]}))),l!==t&&(r.settings.quoteSmart&&At(t,a)>At(t,r.alternative)&&(a=r.alternative),l=a+Y(t,Object.assign({},r.settings.characterReferences,{subset:(a==="'"?_e.single:_e.double)[i][o],attribute:!0}))+a),s+(l&&"="+l))}const Vi=["<","&"];function sr(r,e,t,n){return t&&t.type==="element"&&(t.tagName==="script"||t.tagName==="style")?r.value:Y(r.value,Object.assign({},n.settings.characterReferences,{subset:Vi}))}function Mi(r,e,t,n){return n.settings.allowDangerousHtml?r.value:sr(r,e,t,n)}function Bi(r,e,t,n){return n.all(r)}const Gi=$n("type",{invalid:ji,unknown:Ui,handlers:{comment:li,doctype:ui,element:Di,raw:Mi,root:Bi,text:sr}});function ji(r){throw new Error("Expected node, not `"+r+"`")}function Ui(r){const e=r;throw new Error("Cannot compile unknown node `"+e.type+"`")}const $i={},Wi={},Hi=[];function Fi(r,e){const t=$i,n=t.quote||'"',i=n==='"'?"'":'"';if(n!=='"'&&n!=="'")throw new Error("Invalid quote `"+n+"`, expected `'` or `\"`");return{one:zi,all:qi,settings:{omitOptionalTags:t.omitOptionalTags||!1,allowParseErrors:t.allowParseErrors||!1,allowDangerousCharacters:t.allowDangerousCharacters||!1,quoteSmart:t.quoteSmart||!1,preferUnquoted:t.preferUnquoted||!1,tightAttributes:t.tightAttributes||!1,upperDoctype:t.upperDoctype||!1,tightDoctype:t.tightDoctype||!1,bogusComments:t.bogusComments||!1,tightCommaSeparatedLists:t.tightCommaSeparatedLists||!1,tightSelfClosing:t.tightSelfClosing||!1,collapseEmptyAttributes:t.collapseEmptyAttributes||!1,allowDangerousHtml:t.allowDangerousHtml||!1,voids:t.voids||kn,characterReferences:t.characterReferences||Wi,closeSelfClosing:t.closeSelfClosing||!1,closeEmptyElements:t.closeEmptyElements||!1},schema:t.space==="svg"?rr:Un,quote:n,alternative:i}.one(Array.isArray(r)?{type:"root",children:r}:r,void 0,void 0)}function zi(r,e,t){return Gi(r,e,t,this)}function qi(r){const e=[],t=r&&r.children||Hi;let n=-1;for(;++n<t.length;)e[n]=this.one(t[n],n,r);return e.join("")}function Ki(r){return Array.isArray(r)?r:[r]}function Ce(r,e=!1){var o;const t=r.split(/(\r?\n)/g);let n=0;const i=[];for(let a=0;a<t.length;a+=2){const l=e?t[a]+(t[a+1]||""):t[a];i.push([l,n]),n+=t[a].length,n+=((o=t[a+1])==null?void 0:o.length)||0}return i}function nt(r){return!r||["plaintext","txt","text","plain"].includes(r)}function lr(r){return r==="ansi"||nt(r)}function it(r){return r==="none"}function ur(r){return it(r)}function cr(r,e){var n;if(!e)return r;r.properties||(r.properties={}),(n=r.properties).class||(n.class=[]),typeof r.properties.class=="string"&&(r.properties.class=r.properties.class.split(/\s+/g)),Array.isArray(r.properties.class)||(r.properties.class=[]);const t=Array.isArray(e)?e:e.split(/\s+/g);for(const i of t)i&&!r.properties.class.includes(i)&&r.properties.class.push(i);return r}function Ji(r,e){let t=0;const n=[];for(const i of e)i>t&&n.push({...r,content:r.content.slice(t,i),offset:r.offset+t}),t=i;return t<r.content.length&&n.push({...r,content:r.content.slice(t),offset:r.offset+t}),n}function Xi(r,e){const t=Array.from(e instanceof Set?e:new Set(e)).sort((n,i)=>n-i);return t.length?r.map(n=>n.flatMap(i=>{const o=t.filter(a=>i.offset<a&&a<i.offset+i.content.length).map(a=>a-i.offset).sort((a,l)=>a-l);return o.length?Ji(i,o):i})):r}async function mr(r){return Promise.resolve(typeof r=="function"?r():r).then(e=>e.default||e)}function Pe(r,e){const t=typeof r=="string"?{}:{...r.colorReplacements},n=typeof r=="string"?r:r.name;for(const[i,o]of Object.entries((e==null?void 0:e.colorReplacements)||{}))typeof o=="string"?t[i]=o:i===n&&Object.assign(t,o);return t}function z(r,e){return r&&((e==null?void 0:e[r==null?void 0:r.toLowerCase()])||r)}function pr(r){const e={};return r.color&&(e.color=r.color),r.bgColor&&(e["background-color"]=r.bgColor),r.fontStyle&&(r.fontStyle&H.Italic&&(e["font-style"]="italic"),r.fontStyle&H.Bold&&(e["font-weight"]="bold"),r.fontStyle&H.Underline&&(e["text-decoration"]="underline")),e}function Yi(r){return typeof r=="string"?r:Object.entries(r).map(([e,t])=>`${e}:${t}`).join(";")}function Qi(r){const e=Ce(r,!0).map(([i])=>i);function t(i){if(i===r.length)return{line:e.length-1,character:e[e.length-1].length};let o=i,a=0;for(const l of e){if(o<l.length)break;o-=l.length,a++}return{line:a,character:o}}function n(i,o){let a=0;for(let l=0;l<i;l++)a+=e[l].length;return a+=o,a}return{lines:e,indexToPos:t,posToIndex:n}}class D extends Error{constructor(e){super(e),this.name="ShikiError"}}const dr=new WeakMap;function we(r,e){dr.set(r,e)}function le(r){return dr.get(r)}class ee{constructor(...e){_(this,"_stacks",{});_(this,"lang");if(e.length===2){const[t,n]=e;this.lang=n,this._stacks=t}else{const[t,n,i]=e;this.lang=n,this._stacks={[i]:t}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(e,t){return new ee(Object.fromEntries(Ki(t).map(n=>[n,qe])),e)}getInternalStack(e=this.theme){return this._stacks[e]}get scopes(){return Lt(this._stacks[this.theme])}getScopes(e=this.theme){return Lt(this._stacks[e])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function Lt(r){const e=[],t=new Set;function n(i){var a;if(t.has(i))return;t.add(i);const o=(a=i==null?void 0:i.nameScopesList)==null?void 0:a.scopeName;o&&e.push(o),i.parent&&n(i.parent)}return n(r),e}function Zi(r,e){if(!(r instanceof ee))throw new D("Invalid grammar state");return r.getInternalStack(e)}function eo(){const r=new WeakMap;function e(t){if(!r.has(t.meta)){let n=function(a){if(typeof a=="number"){if(a<0||a>t.source.length)throw new D(`Invalid decoration offset: ${a}. Code length: ${t.source.length}`);return{...i.indexToPos(a),offset:a}}else{const l=i.lines[a.line];if(l===void 0)throw new D(`Invalid decoration position ${JSON.stringify(a)}. Lines length: ${i.lines.length}`);if(a.character<0||a.character>l.length)throw new D(`Invalid decoration position ${JSON.stringify(a)}. Line ${a.line} length: ${l.length}`);return{...a,offset:i.posToIndex(a.line,a.character)}}};const i=Qi(t.source),o=(t.options.decorations||[]).map(a=>({...a,start:n(a.start),end:n(a.end)}));to(o),r.set(t.meta,{decorations:o,converter:i,source:t.source})}return r.get(t.meta)}return{name:"shiki:decorations",tokens(t){var a;if(!((a=this.options.decorations)!=null&&a.length))return;const i=e(this).decorations.flatMap(l=>[l.start.offset,l.end.offset]);return Xi(t,i)},code(t){var m;if(!((m=this.options.decorations)!=null&&m.length))return;const n=e(this),i=Array.from(t.children).filter(d=>d.type==="element"&&d.tagName==="span");if(i.length!==n.converter.lines.length)throw new D(`Number of lines in code element (${i.length}) does not match the number of lines in the source (${n.converter.lines.length}). Failed to apply decorations.`);function o(d,h,p,f){const R=i[d];let E="",y=-1,v=-1;if(h===0&&(y=0),p===0&&(v=0),p===Number.POSITIVE_INFINITY&&(v=R.children.length),y===-1||v===-1)for(let P=0;P<R.children.length;P++)E+=hr(R.children[P]),y===-1&&E.length===h&&(y=P+1),v===-1&&E.length===p&&(v=P+1);if(y===-1)throw new D(`Failed to find start index for decoration ${JSON.stringify(f.start)}`);if(v===-1)throw new D(`Failed to find end index for decoration ${JSON.stringify(f.end)}`);const A=R.children.slice(y,v);if(!f.alwaysWrap&&A.length===R.children.length)l(R,f,"line");else if(!f.alwaysWrap&&A.length===1&&A[0].type==="element")l(A[0],f,"token");else{const P={type:"element",tagName:"span",properties:{},children:A};l(P,f,"wrapper"),R.children.splice(y,A.length,P)}}function a(d,h){i[d]=l(i[d],h,"line")}function l(d,h,p){var E;const f=h.properties||{},R=h.transform||(y=>y);return d.tagName=h.tagName||"span",d.properties={...d.properties,...f,class:d.properties.class},(E=h.properties)!=null&&E.class&&cr(d,h.properties.class),d=R(d,p)||d,d}const s=[],c=n.decorations.sort((d,h)=>h.start.offset-d.start.offset);for(const d of c){const{start:h,end:p}=d;if(h.line===p.line)o(h.line,h.character,p.character,d);else if(h.line<p.line){o(h.line,h.character,Number.POSITIVE_INFINITY,d);for(let f=h.line+1;f<p.line;f++)s.unshift(()=>a(f,d));o(p.line,0,p.character,d)}}s.forEach(d=>d())}}}function to(r){for(let e=0;e<r.length;e++){const t=r[e];if(t.start.offset>t.end.offset)throw new D(`Invalid decoration range: ${JSON.stringify(t.start)} - ${JSON.stringify(t.end)}`);for(let n=e+1;n<r.length;n++){const i=r[n],o=t.start.offset<i.start.offset&&i.start.offset<t.end.offset,a=t.start.offset<i.end.offset&&i.end.offset<t.end.offset,l=i.start.offset<t.start.offset&&t.start.offset<i.end.offset,s=i.start.offset<t.end.offset&&t.end.offset<i.end.offset;if(o||a||l||s){if(a&&a||l&&s)continue;throw new D(`Decorations ${JSON.stringify(t.start)} and ${JSON.stringify(i.start)} intersect.`)}}}}function hr(r){return r.type==="text"?r.value:r.type==="element"?r.children.map(hr).join(""):""}const ro=[eo()];function Te(r){return[...r.transformers||[],...ro]}var q=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],Ue={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function no(r,e){const t=r.indexOf("\x1B[",e);if(t!==-1){const n=r.indexOf("m",t);return{sequence:r.substring(t+2,n).split(";"),startPosition:t,position:n+1}}return{position:r.length}}function St(r,e){let t=1;const n=r[e+t++];let i;if(n==="2"){const o=[r[e+t++],r[e+t++],r[e+t]].map(a=>Number.parseInt(a));o.length===3&&!o.some(a=>Number.isNaN(a))&&(i={type:"rgb",rgb:o})}else if(n==="5"){const o=Number.parseInt(r[e+t]);Number.isNaN(o)||(i={type:"table",index:Number(o)})}return[t,i]}function io(r){const e=[];for(let t=0;t<r.length;t++){const n=r[t],i=Number.parseInt(n);if(!Number.isNaN(i))if(i===0)e.push({type:"resetAll"});else if(i<=9)Ue[i]&&e.push({type:"setDecoration",value:Ue[i]});else if(i<=29){const o=Ue[i-20];o&&e.push({type:"resetDecoration",value:o})}else if(i<=37)e.push({type:"setForegroundColor",value:{type:"named",name:q[i-30]}});else if(i===38){const[o,a]=St(r,t);a&&e.push({type:"setForegroundColor",value:a}),t+=o}else if(i===39)e.push({type:"resetForegroundColor"});else if(i<=47)e.push({type:"setBackgroundColor",value:{type:"named",name:q[i-40]}});else if(i===48){const[o,a]=St(r,t);a&&e.push({type:"setBackgroundColor",value:a}),t+=o}else i===49?e.push({type:"resetBackgroundColor"}):i>=90&&i<=97?e.push({type:"setForegroundColor",value:{type:"named",name:q[i-90+8]}}):i>=100&&i<=107&&e.push({type:"setBackgroundColor",value:{type:"named",name:q[i-100+8]}})}return e}function oo(){let r=null,e=null,t=new Set;return{parse(n){const i=[];let o=0;do{const a=no(n,o),l=a.sequence?n.substring(o,a.startPosition):n.substring(o);if(l.length>0&&i.push({value:l,foreground:r,background:e,decorations:new Set(t)}),a.sequence){const s=io(a.sequence);for(const c of s)c.type==="resetAll"?(r=null,e=null,t.clear()):c.type==="resetForegroundColor"?r=null:c.type==="resetBackgroundColor"?e=null:c.type==="resetDecoration"&&t.delete(c.value);for(const c of s)c.type==="setForegroundColor"?r=c.value:c.type==="setBackgroundColor"?e=c.value:c.type==="setDecoration"&&t.add(c.value)}o=a.position}while(o<n.length);return i}}}var ao={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function so(r=ao){function e(l){return r[l]}function t(l){return`#${l.map(s=>Math.max(0,Math.min(s,255)).toString(16).padStart(2,"0")).join("")}`}let n;function i(){if(n)return n;n=[];for(let c=0;c<q.length;c++)n.push(e(q[c]));let l=[0,95,135,175,215,255];for(let c=0;c<6;c++)for(let m=0;m<6;m++)for(let d=0;d<6;d++)n.push(t([l[c],l[m],l[d]]));let s=8;for(let c=0;c<24;c++,s+=10)n.push(t([s,s,s]));return n}function o(l){return i()[l]}function a(l){switch(l.type){case"named":return e(l.name);case"rgb":return t(l.rgb);case"table":return o(l.index)}}return{value:a}}function lo(r,e,t){const n=Pe(r,t),i=Ce(e),o=so(Object.fromEntries(q.map(l=>{var s;return[l,(s=r.colors)==null?void 0:s[`terminal.ansi${l[0].toUpperCase()}${l.substring(1)}`]]}))),a=oo();return i.map(l=>a.parse(l[0]).map(s=>{let c,m;s.decorations.has("reverse")?(c=s.background?o.value(s.background):r.bg,m=s.foreground?o.value(s.foreground):r.fg):(c=s.foreground?o.value(s.foreground):r.fg,m=s.background?o.value(s.background):void 0),c=z(c,n),m=z(m,n),s.decorations.has("dim")&&(c=uo(c));let d=H.None;return s.decorations.has("bold")&&(d|=H.Bold),s.decorations.has("italic")&&(d|=H.Italic),s.decorations.has("underline")&&(d|=H.Underline),{content:s.value,offset:l[1],color:c,bgColor:m,fontStyle:d}}))}function uo(r){const e=r.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(e)if(e[3]){const n=Math.round(Number.parseInt(e[3],16)/2).toString(16).padStart(2,"0");return`#${e[1]}${e[2]}${n}`}else return e[2]?`#${e[1]}${e[2]}80`:`#${Array.from(e[1]).map(n=>`${n}${n}`).join("")}80`;const t=r.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return t?`var(${t[1]}-dim)`:r}function ot(r,e,t={}){const{lang:n="text",theme:i=r.getLoadedThemes()[0]}=t;if(nt(n)||it(i))return Ce(e).map(s=>[{content:s[0],offset:s[1]}]);const{theme:o,colorMap:a}=r.setTheme(i);if(n==="ansi")return lo(o,e,t);const l=r.getLanguage(n);if(t.grammarState){if(t.grammarState.lang!==l.name)throw new F(`Grammar state language "${t.grammarState.lang}" does not match highlight language "${l.name}"`);if(!t.grammarState.themes.includes(o.name))throw new F(`Grammar state themes "${t.grammarState.themes}" do not contain highlight theme "${o.name}"`)}return mo(e,l,o,a,t)}function co(...r){if(r.length===2)return le(r[1]);const[e,t,n={}]=r,{lang:i="text",theme:o=e.getLoadedThemes()[0]}=n;if(nt(i)||it(o))throw new F("Plain language does not have grammar state");if(i==="ansi")throw new F("ANSI language does not have grammar state");const{theme:a,colorMap:l}=e.setTheme(o),s=e.getLanguage(i);return new ee(Le(t,s,a,l,n).stateStack,s.name,a.name)}function mo(r,e,t,n,i){const o=Le(r,e,t,n,i),a=new ee(Le(r,e,t,n,i).stateStack,e.name,t.name);return we(o.tokens,a),o.tokens}function Le(r,e,t,n,i){const o=Pe(t,i),{tokenizeMaxLineLength:a=0,tokenizeTimeLimit:l=500}=i,s=Ce(r);let c=i.grammarState?Zi(i.grammarState,t.name)??qe:i.grammarContextCode!=null?Le(i.grammarContextCode,e,t,n,{...i,grammarState:void 0,grammarContextCode:void 0}).stateStack:qe,m=[];const d=[];for(let h=0,p=s.length;h<p;h++){const[f,R]=s[h];if(f===""){m=[],d.push([]);continue}if(a>0&&f.length>=a){m=[],d.push([{content:f,offset:R,color:"",fontStyle:0}]);continue}let E,y,v;i.includeExplanation&&(E=e.tokenizeLine(f,c),y=E.tokens,v=0);const A=e.tokenizeLine2(f,c,l),P=A.tokens.length/2;for(let L=0;L<P;L++){const N=A.tokens[2*L],I=L+1<P?A.tokens[2*L+2]:f.length;if(N===I)continue;const U=A.tokens[2*L+1],pe=z(n[Q.getForeground(U)],o),te=Q.getFontStyle(U),ke={content:f.substring(N,I),offset:R+N,color:pe,fontStyle:te};if(i.includeExplanation){const st=[];if(i.includeExplanation!=="scopeName")for(const $ of t.settings){let J;switch(typeof $.scope){case"string":J=$.scope.split(/,/).map(De=>De.trim());break;case"object":J=$.scope;break;default:continue}st.push({settings:$,selectors:J.map(De=>De.split(/ /))})}ke.explanation=[];let lt=0;for(;N+lt<I;){const $=y[v],J=f.substring($.startIndex,$.endIndex);lt+=J.length,ke.explanation.push({content:J,scopes:i.includeExplanation==="scopeName"?po($.scopes):ho(st,$.scopes)}),v+=1}}m.push(ke)}d.push(m),m=[],c=A.ruleStack}return{tokens:d,stateStack:c}}function po(r){return r.map(e=>({scopeName:e}))}function ho(r,e){const t=[];for(let n=0,i=e.length;n<i;n++){const o=e[n];t[n]={scopeName:o,themeMatches:_o(r,o,e.slice(0,n))}}return t}function It(r,e){return r===e||e.substring(0,r.length)===r&&e[r.length]==="."}function fo(r,e,t){if(!It(r[r.length-1],e))return!1;let n=r.length-2,i=t.length-1;for(;n>=0&&i>=0;)It(r[n],t[i])&&(n-=1),i-=1;return n===-1}function _o(r,e,t){const n=[];for(const{selectors:i,settings:o}of r)for(const a of i)if(fo(a,e,t)){n.push(o);break}return n}function fr(r,e,t){const n=Object.entries(t.themes).filter(s=>s[1]).map(s=>({color:s[0],theme:s[1]})),i=n.map(s=>{const c=ot(r,e,{...t,theme:s.theme}),m=le(c),d=typeof s.theme=="string"?s.theme:s.theme.name;return{tokens:c,state:m,theme:d}}),o=go(...i.map(s=>s.tokens)),a=o[0].map((s,c)=>s.map((m,d)=>{const h={content:m.content,variants:{},offset:m.offset};return"includeExplanation"in t&&t.includeExplanation&&(h.explanation=m.explanation),o.forEach((p,f)=>{const{content:R,explanation:E,offset:y,...v}=p[c][d];h.variants[n[f].color]=v}),h})),l=i[0].state?new ee(Object.fromEntries(i.map(s=>{var c;return[s.theme,(c=s.state)==null?void 0:c.getInternalStack(s.theme)]})),i[0].state.lang):void 0;return l&&we(a,l),a}function go(...r){const e=r.map(()=>[]),t=r.length;for(let n=0;n<r[0].length;n++){const i=r.map(s=>s[n]),o=e.map(()=>[]);e.forEach((s,c)=>s.push(o[c]));const a=i.map(()=>0),l=i.map(s=>s[0]);for(;l.every(s=>s);){const s=Math.min(...l.map(c=>c.content.length));for(let c=0;c<t;c++){const m=l[c];m.content.length===s?(o[c].push(m),a[c]+=1,l[c]=i[c][a[c]]):(o[c].push({...m,content:m.content.slice(0,s)}),l[c]={...m,content:m.content.slice(s),offset:m.offset+s})}}}return e}function Se(r,e,t){let n,i,o,a,l,s;if("themes"in t){const{defaultColor:c="light",cssVariablePrefix:m="--shiki-"}=t,d=Object.entries(t.themes).filter(E=>E[1]).map(E=>({color:E[0],theme:E[1]})).sort((E,y)=>E.color===c?-1:y.color===c?1:0);if(d.length===0)throw new F("`themes` option must not be empty");const h=fr(r,e,t);if(s=le(h),c&&!d.find(E=>E.color===c))throw new F(`\`themes\` option must contain the defaultColor key \`${c}\``);const p=d.map(E=>r.getTheme(E.theme)),f=d.map(E=>E.color);o=h.map(E=>E.map(y=>yo(y,f,m,c))),s&&we(o,s);const R=d.map(E=>Pe(E.theme,t));i=d.map((E,y)=>(y===0&&c?"":`${m+E.color}:`)+(z(p[y].fg,R[y])||"inherit")).join(";"),n=d.map((E,y)=>(y===0&&c?"":`${m+E.color}-bg:`)+(z(p[y].bg,R[y])||"inherit")).join(";"),a=`shiki-themes ${p.map(E=>E.name).join(" ")}`,l=c?void 0:[i,n].join(";")}else if("theme"in t){const c=Pe(t.theme,t);o=ot(r,e,t);const m=r.getTheme(t.theme);n=z(m.bg,c),i=z(m.fg,c),a=m.name,s=le(o)}else throw new F("Invalid options, either `theme` or `themes` must be provided");return{tokens:o,fg:i,bg:n,themeName:a,rootStyle:l,grammarState:s}}function yo(r,e,t,n){const i={content:r.content,explanation:r.explanation,offset:r.offset},o=e.map(s=>pr(r.variants[s])),a=new Set(o.flatMap(s=>Object.keys(s))),l={};return o.forEach((s,c)=>{for(const m of a){const d=s[m]||"inherit";if(c===0&&n)l[m]=d;else{const h=m==="color"?"":m==="background-color"?"-bg":`-${m}`,p=t+e[c]+(m==="color"?"":h);l[p]=d}}}),i.htmlStyle=l,i}function Ie(r,e,t,n={meta:{},options:t,codeToHast:(i,o)=>Ie(r,i,o),codeToTokens:(i,o)=>Se(r,i,o)}){var p,f;let i=e;for(const R of Te(t))i=((p=R.preprocess)==null?void 0:p.call(n,i,t))||i;let{tokens:o,fg:a,bg:l,themeName:s,rootStyle:c,grammarState:m}=Se(r,i,t);const{mergeWhitespaces:d=!0}=t;d===!0?o=vo(o):d==="never"&&(o=Ro(o));const h={...n,get source(){return i}};for(const R of Te(t))o=((f=R.tokens)==null?void 0:f.call(h,o))||o;return Eo(o,{...t,fg:a,bg:l,themeName:s,rootStyle:c},h,m)}function Eo(r,e,t,n=le(r)){var f,R,E;const i=Te(e),o=[],a={type:"root",children:[]},{structure:l="classic",tabindex:s="0"}=e;let c={type:"element",tagName:"pre",properties:{class:`shiki ${e.themeName||""}`,style:e.rootStyle||`background-color:${e.bg};color:${e.fg}`,...s!==!1&&s!=null?{tabindex:s.toString()}:{},...Object.fromEntries(Array.from(Object.entries(e.meta||{})).filter(([y])=>!y.startsWith("_")))},children:[]},m={type:"element",tagName:"code",properties:{},children:o};const d=[],h={...t,structure:l,addClassToHast:cr,get source(){return t.source},get tokens(){return r},get options(){return e},get root(){return a},get pre(){return c},get code(){return m},get lines(){return d}};if(r.forEach((y,v)=>{var L,N;v&&(l==="inline"?a.children.push({type:"element",tagName:"br",properties:{},children:[]}):l==="classic"&&o.push({type:"text",value:`
`}));let A={type:"element",tagName:"span",properties:{class:"line"},children:[]},P=0;for(const I of y){let U={type:"element",tagName:"span",properties:{...I.htmlAttrs},children:[{type:"text",value:I.content}]};I.htmlStyle;const pe=Yi(I.htmlStyle||pr(I));pe&&(U.properties.style=pe);for(const te of i)U=((L=te==null?void 0:te.span)==null?void 0:L.call(h,U,v+1,P,A,I))||U;l==="inline"?a.children.push(U):l==="classic"&&A.children.push(U),P+=I.content.length}if(l==="classic"){for(const I of i)A=((N=I==null?void 0:I.line)==null?void 0:N.call(h,A,v+1))||A;d.push(A),o.push(A)}}),l==="classic"){for(const y of i)m=((f=y==null?void 0:y.code)==null?void 0:f.call(h,m))||m;c.children.push(m);for(const y of i)c=((R=y==null?void 0:y.pre)==null?void 0:R.call(h,c))||c;a.children.push(c)}let p=a;for(const y of i)p=((E=y==null?void 0:y.root)==null?void 0:E.call(h,p))||p;return n&&we(p,n),p}function vo(r){return r.map(e=>{const t=[];let n="",i=0;return e.forEach((o,a)=>{const s=!(o.fontStyle&&o.fontStyle&H.Underline);s&&o.content.match(/^\s+$/)&&e[a+1]?(i||(i=o.offset),n+=o.content):n?(s?t.push({...o,offset:i,content:n+o.content}):t.push({content:n,offset:i},o),i=0,n=""):t.push(o)}),t})}function Ro(r){return r.map(e=>e.flatMap(t=>{if(t.content.match(/^\s+$/))return t;const n=t.content.match(/^(\s*)(.*?)(\s*)$/);if(!n)return t;const[,i,o,a]=n;if(!i&&!a)return t;const l=[{...t,offset:t.offset+i.length,content:o}];return i&&l.unshift({content:i,offset:t.offset}),a&&l.push({content:a,offset:t.offset+i.length+o.length}),l}))}function Ao(r,e,t){var o;const n={meta:{},options:t,codeToHast:(a,l)=>Ie(r,a,l),codeToTokens:(a,l)=>Se(r,a,l)};let i=Fi(Ie(r,e,t,n));for(const a of Te(t))i=((o=a.postprocess)==null?void 0:o.call(n,i,t))||i;return i}const Ot={light:"#333333",dark:"#bbbbbb"},Ct={light:"#fffffe",dark:"#1e1e1e"},wt="__shiki_resolved";function at(r){var l,s,c,m,d;if(r!=null&&r[wt])return r;const e={...r};e.tokenColors&&!e.settings&&(e.settings=e.tokenColors,delete e.tokenColors),e.type||(e.type="dark"),e.colorReplacements={...e.colorReplacements},e.settings||(e.settings=[]);let{bg:t,fg:n}=e;if(!t||!n){const h=e.settings?e.settings.find(p=>!p.name&&!p.scope):void 0;(l=h==null?void 0:h.settings)!=null&&l.foreground&&(n=h.settings.foreground),(s=h==null?void 0:h.settings)!=null&&s.background&&(t=h.settings.background),!n&&((c=e==null?void 0:e.colors)!=null&&c["editor.foreground"])&&(n=e.colors["editor.foreground"]),!t&&((m=e==null?void 0:e.colors)!=null&&m["editor.background"])&&(t=e.colors["editor.background"]),n||(n=e.type==="light"?Ot.light:Ot.dark),t||(t=e.type==="light"?Ct.light:Ct.dark),e.fg=n,e.bg=t}e.settings[0]&&e.settings[0].settings&&!e.settings[0].scope||e.settings.unshift({settings:{foreground:e.fg,background:e.bg}});let i=0;const o=new Map;function a(h){var f;if(o.has(h))return o.get(h);i+=1;const p=`#${i.toString(16).padStart(8,"0").toLowerCase()}`;return(f=e.colorReplacements)!=null&&f[`#${p}`]?a(h):(o.set(h,p),p)}e.settings=e.settings.map(h=>{var E,y;const p=((E=h.settings)==null?void 0:E.foreground)&&!h.settings.foreground.startsWith("#"),f=((y=h.settings)==null?void 0:y.background)&&!h.settings.background.startsWith("#");if(!p&&!f)return h;const R={...h,settings:{...h.settings}};if(p){const v=a(h.settings.foreground);e.colorReplacements[v]=h.settings.foreground,R.settings.foreground=v}if(f){const v=a(h.settings.background);e.colorReplacements[v]=h.settings.background,R.settings.background=v}return R});for(const h of Object.keys(e.colors||{}))if((h==="editor.foreground"||h==="editor.background"||h.startsWith("terminal.ansi"))&&!((d=e.colors[h])!=null&&d.startsWith("#"))){const p=a(e.colors[h]);e.colorReplacements[p]=e.colors[h],e.colors[h]=p}return Object.defineProperty(e,wt,{enumerable:!1,writable:!1,value:!0}),e}async function _r(r){return Array.from(new Set((await Promise.all(r.filter(e=>!lr(e)).map(async e=>await mr(e).then(t=>Array.isArray(t)?t:[t])))).flat()))}async function gr(r){return(await Promise.all(r.map(async t=>ur(t)?null:at(await mr(t))))).filter(t=>!!t)}class bo extends wn{constructor(t,n,i,o={}){super(t);_(this,"_resolvedThemes",new Map);_(this,"_resolvedGrammars",new Map);_(this,"_langMap",new Map);_(this,"_langGraph",new Map);_(this,"_textmateThemeCache",new WeakMap);_(this,"_loadedThemesCache",null);_(this,"_loadedLanguagesCache",null);this._resolver=t,this._themes=n,this._langs=i,this._alias=o,this._themes.map(a=>this.loadTheme(a)),this.loadLanguages(this._langs)}getTheme(t){return typeof t=="string"?this._resolvedThemes.get(t):this.loadTheme(t)}loadTheme(t){const n=at(t);return n.name&&(this._resolvedThemes.set(n.name,n),this._loadedThemesCache=null),n}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(t){let n=this._textmateThemeCache.get(t);n||(n=Ee.createFromRawTheme(t),this._textmateThemeCache.set(t,n)),this._syncRegistry.setTheme(n)}getGrammar(t){if(this._alias[t]){const n=new Set([t]);for(;this._alias[t];){if(t=this._alias[t],n.has(t))throw new D(`Circular alias \`${Array.from(n).join(" -> ")} -> ${t}\``);n.add(t)}}return this._resolvedGrammars.get(t)}loadLanguage(t){var a,l,s,c;if(this.getGrammar(t.name))return;const n=new Set([...this._langMap.values()].filter(m=>{var d;return(d=m.embeddedLangsLazy)==null?void 0:d.includes(t.name)}));this._resolver.addLanguage(t);const i={balancedBracketSelectors:t.balancedBracketSelectors||["*"],unbalancedBracketSelectors:t.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(t.scopeName,t);const o=this.loadGrammarWithConfiguration(t.scopeName,1,i);if(o.name=t.name,this._resolvedGrammars.set(t.name,o),t.aliases&&t.aliases.forEach(m=>{this._alias[m]=t.name}),this._loadedLanguagesCache=null,n.size)for(const m of n)this._resolvedGrammars.delete(m.name),this._loadedLanguagesCache=null,(l=(a=this._syncRegistry)==null?void 0:a._injectionGrammars)==null||l.delete(m.scopeName),(c=(s=this._syncRegistry)==null?void 0:s._grammars)==null||c.delete(m.scopeName),this.loadLanguage(this._langMap.get(m.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(t){for(const o of t)this.resolveEmbeddedLanguages(o);const n=Array.from(this._langGraph.entries()),i=n.filter(([o,a])=>!a);if(i.length){const o=n.filter(([a,l])=>{var s;return l&&((s=l.embeddedLangs)==null?void 0:s.some(c=>i.map(([m])=>m).includes(c)))}).filter(a=>!i.includes(a));throw new D(`Missing languages ${i.map(([a])=>`\`${a}\``).join(", ")}, required by ${o.map(([a])=>`\`${a}\``).join(", ")}`)}for(const[o,a]of n)this._resolver.addLanguage(a);for(const[o,a]of n)this.loadLanguage(a)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(t){if(this._langMap.set(t.name,t),this._langGraph.set(t.name,t),t.embeddedLangs)for(const n of t.embeddedLangs)this._langGraph.set(n,this._langMap.get(n))}}class Po{constructor(e,t){_(this,"_langs",new Map);_(this,"_scopeToLang",new Map);_(this,"_injections",new Map);_(this,"_onigLib");this._onigLib={createOnigScanner:n=>e.createScanner(n),createOnigString:n=>e.createString(n)},t.forEach(n=>this.addLanguage(n))}get onigLib(){return this._onigLib}getLangRegistration(e){return this._langs.get(e)}loadGrammar(e){return this._scopeToLang.get(e)}addLanguage(e){this._langs.set(e.name,e),e.aliases&&e.aliases.forEach(t=>{this._langs.set(t,e)}),this._scopeToLang.set(e.scopeName,e),e.injectTo&&e.injectTo.forEach(t=>{this._injections.get(t)||this._injections.set(t,[]),this._injections.get(t).push(e.scopeName)})}getInjections(e){const t=e.split(".");let n=[];for(let i=1;i<=t.length;i++){const o=t.slice(0,i).join(".");n=[...n,...this._injections.get(o)||[]]}return n}}let re=0;function To(r){re+=1,r.warnings!==!1&&re>=10&&re%10===0&&console.warn(`[Shiki] ${re} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let e=!1;if(!r.engine)throw new D("`engine` option is required for synchronous mode");const t=(r.langs||[]).flat(1),n=(r.themes||[]).flat(1).map(at),i=new Po(r.engine,t),o=new bo(i,n,t,r.langAlias);let a;function l(v){E();const A=o.getGrammar(typeof v=="string"?v:v.name);if(!A)throw new D(`Language \`${v}\` not found, you may need to load it first`);return A}function s(v){if(v==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};E();const A=o.getTheme(v);if(!A)throw new D(`Theme \`${v}\` not found, you may need to load it first`);return A}function c(v){E();const A=s(v);a!==v&&(o.setTheme(A),a=v);const P=o.getColorMap();return{theme:A,colorMap:P}}function m(){return E(),o.getLoadedThemes()}function d(){return E(),o.getLoadedLanguages()}function h(...v){E(),o.loadLanguages(v.flat(1))}async function p(...v){return h(await _r(v))}function f(...v){E();for(const A of v.flat(1))o.loadTheme(A)}async function R(...v){return E(),f(await gr(v))}function E(){if(e)throw new D("Shiki instance has been disposed")}function y(){e||(e=!0,o.dispose(),re-=1)}return{setTheme:c,getTheme:s,getLanguage:l,getLoadedThemes:m,getLoadedLanguages:d,loadLanguage:p,loadLanguageSync:h,loadTheme:R,loadThemeSync:f,dispose:y,[Symbol.dispose]:y}}async function Lo(r={}){r.loadWasm;const[e,t,n]=await Promise.all([gr(r.themes||[]),_r(r.langs||[]),r.engine||Dt(r.loadWasm||Hr())]);return To({...r,themes:e,langs:t,engine:n})}async function So(r={}){const e=await Lo(r);return{getLastGrammarState:(...t)=>co(e,...t),codeToTokensBase:(t,n)=>ot(e,t,n),codeToTokensWithThemes:(t,n)=>fr(e,t,n),codeToTokens:(t,n)=>Se(e,t,n),codeToHast:(t,n)=>Ie(e,t,n),codeToHtml:(t,n)=>Ao(e,t,n),...e,getInternalContext:()=>e}}function Io(r,e,t){let n,i,o;{const l=r;n=l.langs,i=l.themes,o=l.engine}async function a(l){function s(p){if(typeof p=="string"){if(lr(p))return[];const f=n[p];if(!f)throw new F(`Language \`${p}\` is not included in this bundle. You may want to load it from external source.`);return f}return p}function c(p){if(ur(p))return"none";if(typeof p=="string"){const f=i[p];if(!f)throw new F(`Theme \`${p}\` is not included in this bundle. You may want to load it from external source.`);return f}return p}const m=(l.themes??[]).map(p=>c(p)),d=(l.langs??[]).map(p=>s(p)),h=await So({engine:l.engine??o(),...l,themes:m,langs:d});return{...h,loadLanguage(...p){return h.loadLanguage(...p.map(s))},loadTheme(...p){return h.loadTheme(...p.map(c))}}}return a}const Do=Io({langs:Pr,themes:Lr,engine:()=>Dt(u(()=>import("./wasm-CG6Dc4jp.js"),[],import.meta.url))});export{u as _,Do as c};
