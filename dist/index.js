var ue=Object.defineProperty;var he=(o,n,r)=>n in o?ue(o,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[n]=r;var V=(o,n,r)=>he(o,typeof n!="symbol"?n+"":n,r);import{r as c,a as B,R as se}from"./assets/react-vendor-CIP6LD3P.js";import{_ as z,c as be}from"./assets/shiki-DBOBms81.js";import{c as T,C as P,P as ge,H as pe,S as fe,a as ye,D as ve,A as je,b as Se,M as Ne,L as G,d as we,X as O,e as Q,R as re,I as Y,B as Ce,f as Le,g as ke,h as W,T as Ee,i as Me,j as X,W as Pe,k as Ie}from"./assets/ui-vendor-CWg08DdH.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&t(i)}).observe(document,{childList:!0,subtree:!0});function r(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function t(s){if(s.ep)return;s.ep=!0;const a=r(s);fetch(s.href,a)}})();var ae={exports:{}},_={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ae=c,Re=Symbol.for("react.element"),ze=Symbol.for("react.fragment"),Oe=Object.prototype.hasOwnProperty,Te=Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,_e={key:!0,ref:!0,__self:!0,__source:!0};function oe(o,n,r){var t,s={},a=null,i=null;r!==void 0&&(a=""+r),n.key!==void 0&&(a=""+n.key),n.ref!==void 0&&(i=n.ref);for(t in n)Oe.call(n,t)&&!_e.hasOwnProperty(t)&&(s[t]=n[t]);if(o&&o.defaultProps)for(t in n=o.defaultProps,n)s[t]===void 0&&(s[t]=n[t]);return{$$typeof:Re,type:o,key:a,ref:i,props:s,_owner:Te.current}}_.Fragment=ze;_.jsx=oe;_.jsxs=oe;ae.exports=_;var e=ae.exports,H={},Z=B;H.createRoot=Z.createRoot,H.hydrateRoot=Z.hydrateRoot;let $=null;const M=()=>typeof window<"u"&&!!window.CSInterface,K=()=>{if(!$&&M())try{$=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(o){console.error("Failed to initialize CSInterface:",o)}return $},ne=()=>{if(!M()){console.warn("Not running in CEP environment");return}const o=K();if(!o)return;o.addEventListener("com.adobe.csxs.events.ThemeColorChanged",r=>{console.log("Theme changed:",r)});const n=o.getHostEnvironment();console.log("Host environment:",n),o.evalScript("SahAI.getAppInfo()",r=>{try{if(!r||r.trim()===""){console.warn("Empty response from ExtendScript");return}const t=JSON.parse(r);console.log("ExtendScript response:",t)}catch(t){console.error("Failed to parse ExtendScript response:",t,"Raw result:",r)}})},L=(o,n=3e4,r=2)=>new Promise((t,s)=>{const a=K();if(!a){s(new Error("CSInterface not available - not running in CEP environment"));return}let i=0;const l=()=>{i++;const h=setTimeout(()=>{i<=r?(console.warn(`ExtendScript execution attempt ${i} timed out, retrying...`),l()):s(new Error(`ExtendScript execution timed out after ${n}ms (${r+1} attempts)`))},n);try{a.evalScript(o,x=>{clearTimeout(h);try{if(typeof x=="string"&&x.startsWith("EvalScript error")){if(i<=r){console.warn(`ExtendScript error on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(`ExtendScript Error: ${x}`));return}if(!x||x.trim()===""){if(i<=r){console.warn(`Empty response on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error("Empty response from ExtendScript after all retries"));return}let g;try{g=JSON.parse(x)}catch{g={success:!0,data:x}}if(typeof g=="object"&&g!==null)if(g.success===!1){if(i<=r){console.warn(`ExtendScript returned failure on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(g.message||"ExtendScript execution failed"))}else t(g);else t({success:!0,data:g})}catch(g){if(i<=r){console.warn(`Error processing response on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(`Failed to process ExtendScript response: ${g}`))}})}catch(x){if(clearTimeout(h),i<=r){console.warn(`Error executing ExtendScript on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(`Failed to execute ExtendScript: ${x}`))}};l()});class k{static async save(n){const r=JSON.stringify(n);try{if(M())try{const t=await L(`saveSettings(${JSON.stringify(n)})`,1e4);if(t.success)console.log("Settings saved to CEP storage successfully");else throw new Error(t.message||"CEP save failed")}catch(t){console.warn("CEP storage save failed, falling back to localStorage:",t)}localStorage.setItem(this.SETTINGS_KEY,r),console.log("Settings saved to localStorage successfully")}catch(t){console.error("All settings save methods failed:",t);try{localStorage.setItem(this.SETTINGS_KEY,r)}catch(s){throw new Error(`Failed to save settings: ${t}. LocalStorage also failed: ${s}`)}}}static async load(){try{if(M())try{const r=await L("loadSettings()",1e4);if(r.success&&r.data)return console.log("Settings loaded from CEP storage successfully"),r.data}catch(r){console.warn("CEP storage load failed, falling back to localStorage:",r)}const n=localStorage.getItem(this.SETTINGS_KEY);if(n){const r=JSON.parse(n);return console.log("Settings loaded from localStorage successfully"),r}return console.log("No existing settings found, returning defaults"),{providers:[]}}catch(n){return console.error("All settings load methods failed:",n),{providers:[]}}}static async exportSettings(){const n=await this.load();return JSON.stringify(n,null,2)}static async importSettings(n){try{const r=JSON.parse(n);await this.save(r)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(M())try{await L("saveSettings({})",1e4)}catch(n){console.warn("Failed to clear CEP storage:",n)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(n){throw new Error(`Failed to clear settings: ${n}`)}}}V(k,"SETTINGS_KEY","sahAI_settings");class U{static async checkProviderStatus(n,r){const t=Date.now();try{const{ProviderBridge:s}=await z(async()=>{const{ProviderBridge:i}=await Promise.resolve().then(()=>ie);return{ProviderBridge:i}},void 0,import.meta.url);return{isOnline:(await s.listModels(n,r.baseURL,r.apiKey)).length>0,latency:Date.now()-t}}catch(s){return{isOnline:!1,error:s.message||String(s),latency:Date.now()-t}}}}const De={async listModels(o,n,r){try{const t=`listModels('${o}', '${n||""}', '${r||""}')`,s=await L(t,15e3);if(console.log(`ProviderBridge.listModels result for ${o}:`,s),s&&typeof s=="object"){if(s.success&&s.data){let a;if(typeof s.data=="string")try{a=JSON.parse(s.data)}catch(i){return console.error("Failed to parse ExtendScript response:",i),this.getFallbackModels(o)}else a=s.data;if(a&&a.ok&&Array.isArray(a.models))return a.models.map(i=>({id:i.id,name:i.name,description:i.description||"",contextLength:i.context_length||4096,isRecommended:i.is_recommended||!1}))}if(s.ok&&Array.isArray(s.models))return s.models.map(a=>({id:a.id,name:a.name,description:a.description||"",contextLength:a.context_length||4096,isRecommended:a.is_recommended||!1}))}return console.warn(`Failed to fetch models for ${o}, using fallback`),this.getFallbackModels(o)}catch(t){return console.error(`Error fetching models for ${o}:`,t),this.getFallbackModels(o)}},getFallbackModels(o){return{openai:[{id:"gpt-4o",name:"GPT-4o",description:"Most capable OpenAI model",contextLength:128e3,isRecommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster, more affordable",contextLength:128e3,isRecommended:!1},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Legacy model",contextLength:16384,isRecommended:!1}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Anthropic's most capable model",contextLength:2e5,isRecommended:!0},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fast and efficient",contextLength:2e5,isRecommended:!1},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Powerful reasoning",contextLength:2e5,isRecommended:!1}],gemini:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,isRecommended:!1}],groq:[{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Fast inference",contextLength:131072,isRecommended:!1},{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Balanced performance",contextLength:131072,isRecommended:!0},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Large context",contextLength:32768,isRecommended:!1}],deepseek:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose",contextLength:128e3,isRecommended:!0},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Code-focused",contextLength:128e3,isRecommended:!1}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",description:"Most capable",contextLength:128e3,isRecommended:!0},{id:"mistral-medium-latest",name:"Mistral Medium",description:"Balanced",contextLength:32e3,isRecommended:!1},{id:"mistral-small-latest",name:"Mistral Small",description:"Fast and efficient",contextLength:32e3,isRecommended:!1}],moonshot:[{id:"moonshot-v1-128k",name:"Moonshot v1 128K",description:"Large context",contextLength:128e3,isRecommended:!0},{id:"moonshot-v1-32k",name:"Moonshot v1 32K",description:"Medium context",contextLength:32e3,isRecommended:!1}],openrouter:[{id:"openai/gpt-4o",name:"GPT-4o (OpenRouter)",description:"OpenAI via OpenRouter",contextLength:128e3,isRecommended:!0},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (OpenRouter)",description:"Anthropic via OpenRouter",contextLength:2e5,isRecommended:!1}],perplexity:[{id:"llama-3.1-sonar-large-128k-online",name:"Llama 3.1 Sonar Large 128K Online",description:"Large online model",contextLength:128e3,isRecommended:!0},{id:"llama-3.1-sonar-small-128k-online",name:"Llama 3.1 Sonar Small 128K Online",description:"Small online model",contextLength:128e3,isRecommended:!1}],qwen:[{id:"qwen-max",name:"Qwen Max",description:"Most capable",contextLength:32e3,isRecommended:!0},{id:"qwen-plus",name:"Qwen Plus",description:"Balanced performance",contextLength:32e3,isRecommended:!1},{id:"qwen-turbo",name:"Qwen Turbo",description:"Fast and efficient",contextLength:8e3,isRecommended:!1}],together:[{id:"meta-llama/Llama-3-70b-chat-hf",name:"Llama 3 70B Chat",description:"Large language model",contextLength:8192,isRecommended:!0},{id:"meta-llama/Llama-3-8b-chat-hf",name:"Llama 3 8B Chat",description:"Smaller, faster model",contextLength:8192,isRecommended:!1}],vertex:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,isRecommended:!1}],xai:[{id:"grok-beta",name:"Grok Beta",description:"xAI's flagship model",contextLength:128e3,isRecommended:!0},{id:"grok-vision-beta",name:"Grok Vision Beta",description:"Vision-capable model",contextLength:128e3,isRecommended:!1}],ollama:[{id:"llama3.1",name:"Llama 3.1",description:"Open source LLM",contextLength:4096,isRecommended:!0},{id:"mistral",name:"Mistral",description:"Efficient transformer",contextLength:8192,isRecommended:!1},{id:"codellama",name:"Code Llama",description:"Code-focused",contextLength:16384,isRecommended:!1}],lmstudio:[{id:"local-model",name:"Local Model",description:"Your local model",contextLength:4096,isRecommended:!0}]}[o]||[]}},ie=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:k,ProviderBridge:De,ProviderStatusChecker:U,executeExtendScript:L,getCSInterface:K,initializeCEP:ne,isCEPEnvironment:M},Symbol.toStringTag,{value:"Module"})),R=T((o,n)=>({providers:[{id:"openai",name:"OpenAI",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",isConfigured:!1,models:[]},{id:"groq",name:"Groq",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",isConfigured:!1,models:[]},{id:"mistral",name:"Mistral",isConfigured:!1,models:[]},{id:"moonshot",name:"Moonshot AI",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",isConfigured:!1,models:[]},{id:"perplexity",name:"Perplexity",isConfigured:!1,models:[]},{id:"qwen",name:"Alibaba Qwen",isConfigured:!1,models:[]},{id:"together",name:"Together AI",isConfigured:!1,models:[]},{id:"vertex",name:"Google Vertex AI",isConfigured:!1,models:[]},{id:"xai",name:"xAI",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",isConfigured:!1,models:[]},{id:"lmstudio",name:"LM Studio",isConfigured:!1,models:[]}],activeProviderId:void 0,isLoadingModels:!1,setActiveProvider:r=>{o({activeProviderId:r}),n().persistSettings()},updateProviderConfig:(r,t)=>{o(s=>({providers:s.providers.map(a=>a.id===r?{...a,...t,isConfigured:!!t.apiKey}:a)})),n().persistSettings()},setProviderModels:(r,t)=>{o(s=>({providers:s.providers.map(a=>a.id===r?{...a,models:t,isLoading:!1,error:void 0}:a)}))},setSelectedModel:(r,t)=>{o(s=>({providers:s.providers.map(a=>a.id===r?{...a,selectedModelId:t}:a)})),n().persistSettings()},updateProviderKey:(r,t,s)=>{o(a=>({providers:a.providers.map(i=>i.id===r?{...i,apiKey:t,isConfigured:!!t,selectedModelId:s||i.selectedModelId}:i)})),n().persistSettings()},saveProviderSelection:(r,t)=>{o(s=>({activeProviderId:r,providers:s.providers.map(a=>a.id===r?{...a,...t,isConfigured:!!(t.apiKey||a.baseURL)}:a)})),n().persistSettings()},loadModelsForProvider:async r=>{const t=n().providers.find(s=>s.id===r);if(!(t!=null&&t.isConfigured)){console.warn(`Provider ${r} is not configured, skipping model loading`);return}console.log(`Loading models for provider: ${r}`),o(s=>({providers:s.providers.map(a=>a.id===r?{...a,isLoading:!0,error:void 0}:a)}));try{const{ProviderBridge:s}=await z(async()=>{const{ProviderBridge:l}=await Promise.resolve().then(()=>ie);return{ProviderBridge:l}},void 0,import.meta.url),a=await s.listModels(r,t.baseURL,t.apiKey);console.log(`Received ${a.length} models for ${r}:`,a);const i=a.map(l=>({id:l.id,name:l.name,description:l.description||"",contextLength:l.contextLength||4096,isRecommended:l.isRecommended||!1}));console.log(`Transformed models for ${r}:`,i),n().setProviderModels(r,i)}catch(s){console.error(`Error loading models for ${r}:`,s);const a=(s==null?void 0:s.message)||String(s),i=a.includes("timeout")?"Request timed out. Please check your internet connection and try again.":a.includes("network")?"Network error. Please check your internet connection.":a.includes("unauthorized")||a.includes("401")?"Invalid API key. Please check your credentials.":a.includes("forbidden")||a.includes("403")?"Access denied. Please check your API key permissions.":a.includes("not found")||a.includes("404")?"API endpoint not found. Please check the provider configuration.":a;o(l=>({providers:l.providers.map(h=>h.id===r?{...h,isLoading:!1,error:i}:h)}))}},persistSettings:()=>{const{activeProviderId:r,providers:t}=n();k.save({activeProviderId:r,providers:t.map(s=>({id:s.id,isConfigured:s.isConfigured,apiKey:s.apiKey,baseURL:s.baseURL,selectedModelId:s.selectedModelId,settings:s.settings}))})},loadSettings:async()=>{try{const r=await k.load();r.activeProviderId&&o({activeProviderId:r.activeProviderId}),r.providers&&Array.isArray(r.providers)&&o(t=>({providers:t.providers.map(s=>{var i;const a=(i=r.providers)==null?void 0:i.find(l=>l.id===s.id);return a?{...s,...a}:s})}))}catch(r){console.error("Failed to load CEP settings:",r)}},getActiveProvider:()=>{const{providers:r,activeProviderId:t}=n();return r.find(s=>s.id===t)||null},getActiveModel:()=>{const r=n().getActiveProvider();return r!=null&&r.selectedModelId&&r.models.find(t=>t.id===r.selectedModelId)||null}})),I=T(o=>({modal:null,openModal:n=>o({modal:n}),closeModal:()=>o({modal:null})})),J=T((o,n)=>({messages:[],isLoading:!1,addMessage:r=>{const t={...r,id:crypto.randomUUID(),timestamp:Date.now()};o(a=>({messages:[...a.messages,t]}));const s=n().currentSession;s&&z(async()=>{const{useHistoryStore:a}=await Promise.resolve().then(()=>te);return{useHistoryStore:a}},void 0,import.meta.url).then(({useHistoryStore:a})=>{var h;const i=a.getState(),l=i.sessions.find(x=>x.id===s);if(l){const x={...l,messages:[...n().messages],updatedAt:Date.now(),title:l.title===`Chat ${new Date(l.createdAt).toLocaleDateString()}`&&((h=n().messages[0])==null?void 0:h.content.slice(0,50))+"..."||l.title};i.saveSession(x)}})},setLoading:r=>o({isLoading:r}),createNewSession:()=>{const r=crypto.randomUUID();return o({messages:[],currentSession:r}),z(async()=>{const{useHistoryStore:t}=await Promise.resolve().then(()=>te);return{useHistoryStore:t}},void 0,import.meta.url).then(({useHistoryStore:t})=>{t.getState().createSession()}),r},loadSession:(r,t)=>{o({currentSession:r,messages:t})},clearMessages:()=>{o({messages:[],currentSession:void 0})}})),$e=()=>{const{getActiveProvider:o}=R(),[n,r]=c.useState({isOnline:null,isChecking:!1}),t=o();c.useEffect(()=>{let a;const i=async()=>{if(!(t!=null&&t.isConfigured)){r({isOnline:null,isChecking:!1});return}r(l=>({...l,isChecking:!0,error:void 0}));try{const l=await U.checkProviderStatus(t.id,{apiKey:t.apiKey,baseURL:t.baseURL});r({isOnline:l.isOnline,latency:l.latency,isChecking:!1})}catch(l){r({isOnline:!1,isChecking:!1,error:l.message})}};return t!=null&&t.isConfigured&&(i(),a=setInterval(i,3e4)),()=>{a&&clearInterval(a)}},[t]);const s=()=>{const a="w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-pointer";return n.isChecking?`${a} bg-adobe-warning animate-pulse shadow-adobe-warning/40`:n.isOnline===!0?`${a} bg-adobe-success shadow-adobe-success/40`:`${a} bg-adobe-error shadow-adobe-error/40`};return e.jsx("div",{className:s()})},Fe=()=>{const{getActiveProvider:o,getActiveModel:n,loadSettings:r}=R(),{openModal:t}=I(),{createNewSession:s}=J(),a=o(),i=n(),l=c.useMemo(()=>a?i?`${a.name} • ${i.name}`:`${a.name} • Select Model`:"Select AI Provider & Model",[a,i]);return c.useEffect(()=>{r()},[r]),e.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>t("status"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all flex items-center justify-center",children:e.jsx($e,{})}),e.jsxs("button",{onClick:()=>t("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[e.jsx("span",{className:"max-w-[300px] truncate",children:l}),e.jsx(P,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:s,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:e.jsx(ge,{size:16})}),e.jsx("div",{className:"h-4 w-px bg-adobe-border"}),e.jsx("button",{onClick:()=>t("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:e.jsx(pe,{size:16})}),e.jsx("button",{onClick:()=>t("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:e.jsx(fe,{size:16})})]})]})},Be=["javascript","typescript","jsx","tsx","html","css","scss","less","json","jsonc","xml","yaml","markdown","python","swift","rust","go","java","php","ruby","shell","actionscript-3"],He=["github-dark"],Ge=be({themes:He,langs:Be});let F=null;async function Ke(){return F||(F=await Ge),F}function de(o){return["javascript","typescript","jsx","tsx","html","css","scss","less","json","jsonc","xml","yaml","markdown","python","swift","rust","go","java","php","ruby","shell","actionscript-3","actionscript"].includes(o.toLowerCase())}function Ue(o){const n={js:"javascript",ts:"typescript",bash:"shell",sh:"shell",zsh:"shell",fish:"shell",py:"python",rb:"ruby",yml:"yaml",htm:"html",sass:"scss",jsx:"jsx",tsx:"tsx"},r=o.toLowerCase();return n[r]?n[r]:de(r)?r:"text"}const Je=({content:o})=>{const[n,r]=c.useState(null);if(c.useEffect(()=>{Ke().then(r)},[]),!n)return e.jsx("pre",{className:"whitespace-pre-wrap",children:o});const t=o.split(/(```[\s\S]*?```)/g);return e.jsx(e.Fragment,{children:t.map((s,a)=>{if(s.startsWith("```")){const i=s.split(`
`),l=i[0].replace("```","").trim(),h=i.slice(1,-1).join(`
`),x=de(l)?l:Ue(l);return e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"absolute top-1 right-1 flex gap-1",children:[e.jsx("button",{title:"Copy",onClick:()=>navigator.clipboard.writeText(h),className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:e.jsx(ye,{size:14})}),e.jsx("button",{title:"Save",className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:e.jsx(ve,{size:14})})]}),e.jsx("div",{dangerouslySetInnerHTML:{__html:n.codeToHtml(h,{lang:x,theme:"github-dark"})}})]},a)}return e.jsx("div",{children:s},a)})})},qe=({message:o})=>{const n=o.role==="user";return e.jsx("div",{className:`flex gap-3 ${n?"justify-end":"justify-start"} mb-4`,children:e.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${n?"bg-adobe-bg-tertiary text-adobe-text-primary ml-8 rounded-br-md":"bg-adobe-bg-primary text-adobe-text-primary mr-8 rounded-bl-md border border-adobe-border"}`,children:e.jsx("div",{className:"whitespace-pre-wrap",children:e.jsx(Je,{content:o.content})})})})},Ve=""+new URL("assets/BrandLogo-BoAYSo97.svg",import.meta.url).href,Qe=()=>{const{messages:o,isLoading:n,currentSession:r}=J(),t=c.useRef(null),s=c.useRef(null),[a,i]=c.useState(!1),l=c.useRef();c.useEffect(()=>{var x;(x=t.current)==null||x.scrollIntoView({behavior:"smooth"})},[o,n]),c.useEffect(()=>{const x=s.current;if(!x)return;const g=()=>{clearTimeout(l.current);const{scrollTop:y,scrollHeight:p,clientHeight:j}=x,S=p-(y+j)<100;i(!S),l.current=setTimeout(()=>{i(!1)},2e3)};return x.addEventListener("scroll",g),()=>{x.removeEventListener("scroll",g),clearTimeout(l.current)}},[]);const h=()=>{var x;(x=t.current)==null||x.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{ref:s,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!r||o.length===0)&&e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[e.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:e.jsx("img",{src:Ve,alt:"SahAI Logo",className:"w-20 h-20 brightness-0 invert"})}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),e.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),o.map(x=>e.jsx(qe,{message:x},x.id)),n&&e.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:e.jsx("span",{children:"AI is thinking..."})}),e.jsx("div",{ref:t}),a&&e.jsx("button",{onClick:h,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:e.jsx(je,{size:18})})]})},Ye=se.memo(({onAttachFile:o,onVoiceInput:n})=>{const[r,t]=c.useState(""),[s,a]=c.useState(!1),i=c.useRef(null),{addMessage:l,isLoading:h,setLoading:x,currentSession:g,createNewSession:y}=J(),p=4e3,j=!r.trim(),S=r.length>p*.9;c.useEffect(()=>{const d=i.current;d&&d.style.setProperty("--textarea-height","72px")},[]);const C=c.useCallback(()=>{const d=i.current;if(!d)return;d.style.height="auto";const m=Math.min(Math.max(d.scrollHeight,72),200);d.style.setProperty("--textarea-height",`${m}px`),d.style.height=""},[]),v=c.useCallback(d=>{t(d.target.value),C()},[C]),N=c.useCallback(async()=>{const d=r.trim();if(!(!d||h)){t(""),i.current&&i.current.style.setProperty("--textarea-height","72px");try{x(!0),g||y(),l({content:d,role:"user"}),setTimeout(()=>{l({content:`Echo: ${d}`,role:"assistant"}),x(!1)},1e3)}catch{t(d),x(!1)}}},[r,h,g,l,x,y]),b=c.useCallback(d=>{d.key==="Enter"&&!d.shiftKey&&!s&&(d.preventDefault(),N())},[N,s]);return e.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[e.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[e.jsx("div",{className:"flex items-center pl-3",children:e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:h,children:e.jsx(Se,{size:18})})}),e.jsx("textarea",{ref:i,rows:3,maxLength:p,value:r,onChange:v,onKeyDown:b,onCompositionStart:()=>a(!0),onCompositionEnd:()=>a(!1),placeholder:"Type a message...",className:`flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80
            auto-resize-textarea leading-relaxed overflow-y-auto chat-messages-scrollbar`}),e.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[e.jsx("button",{onClick:n,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:h,children:e.jsx(Ne,{size:18})}),e.jsx("button",{onClick:N,disabled:j||h,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:h?e.jsx(G,{size:18,className:"animate-spin"}):e.jsx(we,{size:18})})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[e.jsxs("span",{className:`text-xs ${S?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[r.length,"/",p]}),e.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),We=({provider:o,size:n=16,className:r="",...t})=>{const s={width:n,height:n,viewBox:"0 0 24 24",fill:"currentColor",className:`provider-logo ${r}`,...t};switch(o){case"openai":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"})});case"anthropic":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 22h4l2-5h8l2 5h4L12 2zm0 6l2.5 6h-5L12 8z"})});case"gemini":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"groq":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})});case"deepseek":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l10 18H2L12 2zm0 3.5L5.5 18h13L12 5.5z"})});case"mistral":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"moonshot":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3-3-8z"})});case"openrouter":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41z"})});case"perplexity":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6H9l3-3 3 3h-2v6z"})});case"qwen":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"together":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm-6 0c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z"})});case"vertex":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"xai":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"})});case"ollama":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"})});case"lmstudio":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})});default:return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}},ee=[{value:"openai",label:"OpenAI"},{value:"anthropic",label:"Anthropic"},{value:"gemini",label:"Google Gemini"},{value:"groq",label:"Groq"},{value:"deepseek",label:"DeepSeek"},{value:"mistral",label:"Mistral"},{value:"moonshot",label:"Moonshot AI"},{value:"openrouter",label:"OpenRouter"},{value:"perplexity",label:"Perplexity"},{value:"qwen",label:"Alibaba Qwen"},{value:"together",label:"Together AI"},{value:"vertex",label:"Google Vertex AI"},{value:"xai",label:"xAI"},{value:"ollama",label:"Ollama"},{value:"lmstudio",label:"LM Studio"}],Xe=()=>{const{closeModal:o}=I(),{providers:n,saveProviderSelection:r,loadModelsForProvider:t}=R(),[s,a]=c.useState(""),[i,l]=c.useState(""),[h,x]=c.useState(""),[g,y]=c.useState(""),[p,j]=c.useState(""),[S,C]=c.useState(!1),[v,N]=c.useState(!1),b=c.useRef(null),d=c.useRef(null),m=n.find(u=>u.id===s),f=(m==null?void 0:m.models)||[],w=ee.filter(u=>u.label.toLowerCase().includes(g.toLowerCase())),le=f.filter(u=>u.name.toLowerCase().includes(p.toLowerCase()));c.useEffect(()=>{const u=E=>{b.current&&!b.current.contains(E.target)&&C(!1),d.current&&!d.current.contains(E.target)&&N(!1)};return document.addEventListener("mousedown",u),()=>document.removeEventListener("mousedown",u)},[]),c.useEffect(()=>{!s||!h.trim()||(r(s,{apiKey:h}),t(s))},[s,h,r,t]);const ce=c.useCallback(u=>{var A;const E=((A=ee.find(D=>D.value===u))==null?void 0:A.label)||"";a(u),y(E),l(""),j(""),x(""),C(!1)},[]),me=c.useCallback(u=>{var A;const E=((A=f.find(D=>D.id===u))==null?void 0:A.name)||"";l(u),j(E),N(!1)},[f]),xe=c.useCallback(()=>{!s||!i||!h.trim()||(r(s,{apiKey:h,selectedModelId:i}),o())},[s,i,h,r,o]);return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl relative",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"AI Provider Configuration"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]}),e.jsxs("div",{className:"p-6 space-y-6 overflow-visible",children:[e.jsxs("div",{className:"relative",ref:b,children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Provider"}),e.jsxs("button",{onClick:()=>C(u=>!u),className:"w-full flex items-center justify-between bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-left text-adobe-text-primary",children:[e.jsx("span",{children:g||"Select provider"}),e.jsx(P,{size:16,className:`transition-transform ${S?"rotate-180":""}`})]}),S&&B.createPortal(e.jsx("div",{className:"fixed inset-0 z-50",onClick:()=>C(!1),children:e.jsxs("div",{className:"absolute bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",style:{top:b.current?b.current.getBoundingClientRect().bottom+4:0,left:b.current?b.current.getBoundingClientRect().left:0,width:b.current?b.current.getBoundingClientRect().width:"auto"},onClick:u=>u.stopPropagation(),children:[e.jsx("input",{type:"text",placeholder:"Type to filter…",className:"w-full px-4 py-2 bg-adobe-bg-tertiary border-0 border-b border-adobe-border text-sm text-adobe-text-primary outline-none",value:g,onChange:u=>y(u.target.value),onKeyDown:u=>u.stopPropagation()}),w.map(u=>e.jsxs("div",{className:"px-4 py-3 cursor-pointer flex items-center gap-3 hover:bg-adobe-bg-tertiary",onClick:()=>ce(u.value),children:[e.jsx(We,{provider:u.value,size:16}),e.jsx("span",{children:u.label})]},u.value))]})}),document.body)]}),e.jsxs("div",{className:"relative",ref:d,children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Model"}),e.jsxs("button",{onClick:()=>s&&N(u=>!u),disabled:!s,className:"w-full flex items-center justify-between bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-left disabled:opacity-50",children:[e.jsx("span",{className:"truncate",children:p||"Select model"}),e.jsx(P,{size:16,className:`transition-transform ${v?"rotate-180":""}`})]}),v&&s&&B.createPortal(e.jsx("div",{className:"fixed inset-0 z-50",onClick:()=>N(!1),children:e.jsxs("div",{className:"absolute bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",style:{top:d.current?d.current.getBoundingClientRect().bottom+4:0,left:d.current?d.current.getBoundingClientRect().left:0,width:d.current?d.current.getBoundingClientRect().width:"auto"},onClick:u=>u.stopPropagation(),children:[e.jsx("input",{type:"text",placeholder:"Filter models…",className:"w-full px-4 py-2 bg-adobe-bg-tertiary border-0 border-b border-adobe-border text-sm text-adobe-text-primary outline-none",value:p,onChange:u=>j(u.target.value),onKeyDown:u=>u.stopPropagation()}),(m==null?void 0:m.isLoading)&&f.length===0&&e.jsxs("div",{className:"px-4 py-3 flex items-center space-x-2 text-adobe-text-secondary",children:[e.jsx(G,{size:16,className:"animate-spin"}),e.jsx("span",{children:"Loading models..."})]}),(m==null?void 0:m.error)&&f.length===0&&e.jsxs("div",{className:"px-4 py-3",children:[e.jsx("div",{className:"text-red-400 text-sm font-medium",children:"Failed to load models"}),e.jsx("div",{className:"text-adobe-text-secondary text-xs mt-1",children:m.error}),e.jsx("button",{onClick:()=>t(s),className:"text-adobe-accent text-xs mt-2 hover:underline",children:"Retry"})]}),le.map(u=>e.jsxs("div",{className:"px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary",onClick:()=>me(u.id),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:u.name}),u.isRecommended&&e.jsx("span",{className:"text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded",children:"Recommended"})]}),u.description&&e.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1",children:u.description})]},u.id))]})}),document.body)]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"API Key"}),e.jsx("input",{type:"password",value:h,onChange:u=>x(u.target.value),placeholder:"Enter your API key",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[e.jsx("button",{onClick:o,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"Cancel"}),e.jsx("button",{onClick:xe,disabled:!s||!i||!h.trim(),className:"px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:"Save & Close"})]})]})})},q=T((o,n)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{o({isLoading:!0,error:null});try{if(!window.CSInterface){const s=localStorage.getItem("sahai-chat-history"),a=s?JSON.parse(s):[];o({sessions:a,isLoading:!1});return}const t=await L("loadHistory()");if(t&&t.success&&t.data){const s=Array.isArray(t.data)?t.data:[];o({sessions:s,isLoading:!1})}else if(t&&t.success)o({sessions:[],isLoading:!1});else throw new Error((t==null?void 0:t.message)||"Failed to load history from ExtendScript")}catch(r){console.error("Failed to load history:",r);try{const t=localStorage.getItem("sahai-chat-history"),s=t?JSON.parse(t):[];o({sessions:s,isLoading:!1,error:`Using local storage fallback: ${r.message}`})}catch{o({error:r.message||"Failed to load chat history",isLoading:!1,sessions:[]})}}},saveSession:async r=>{try{o(a=>({sessions:a.sessions.some(i=>i.id===r.id)?a.sessions.map(i=>i.id===r.id?r:i):[...a.sessions,r]}));const t=n().sessions;if(!window.CSInterface){localStorage.setItem("sahai-chat-history",JSON.stringify(t));return}await L(`saveHistory(${JSON.stringify(t)})`),localStorage.setItem("sahai-chat-history",JSON.stringify(t))}catch(t){console.error("Failed to save session:",t);try{const s=n().sessions;localStorage.setItem("sahai-chat-history",JSON.stringify(s))}catch{o({error:t.message||"Failed to save session"})}}},deleteSession:async r=>{try{o(a=>({sessions:a.sessions.filter(i=>i.id!==r),currentSessionId:a.currentSessionId===r?null:a.currentSessionId}));const t=n().sessions;if(!window.CSInterface){localStorage.setItem("sahai-chat-history",JSON.stringify(t));return}await L(`saveHistory(${JSON.stringify(t)})`),localStorage.setItem("sahai-chat-history",JSON.stringify(t))}catch(t){console.error("Failed to delete session:",t);try{const s=n().sessions;localStorage.setItem("sahai-chat-history",JSON.stringify(s))}catch{o({error:t.message||"Failed to delete session"})}}},clearHistory:async()=>{try{if(o({sessions:[],currentSessionId:null}),!window.CSInterface){localStorage.setItem("sahai-chat-history",JSON.stringify([]));return}await L("saveHistory([])"),localStorage.setItem("sahai-chat-history",JSON.stringify([]))}catch(r){console.error("Failed to clear history:",r);try{localStorage.setItem("sahai-chat-history",JSON.stringify([]))}catch{o({error:r.message||"Failed to clear history"})}}},createSession:r=>{const t={id:crypto.randomUUID(),title:r||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};o(a=>({sessions:[t,...a.sessions],currentSessionId:t.id}));const{saveSession:s}=n();return s(t),t},updateSession:(r,t)=>{o(s=>({sessions:s.sessions.map(a=>a.id===r?{...a,...t,updatedAt:Date.now()}:a)}))},setCurrentSession:r=>{o({currentSessionId:r})},getCurrentSession:()=>{const{sessions:r,currentSessionId:t}=n();return r.find(s=>s.id===t)||null},getSessionById:r=>{const{sessions:t}=n();return t.find(s=>s.id===r)||null},getSortedSessions:()=>{const{sessions:r}=n();return[...r].sort((t,s)=>s.updatedAt-t.updatedAt)}})),te=Object.freeze(Object.defineProperty({__proto__:null,useHistoryStore:q},Symbol.toStringTag,{value:"Module"})),Ze=()=>{const{closeModal:o}=I(),{sessions:n}=q(),[r,t]=c.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[s,a]=c.useState("settings"),[i,l]=c.useState(!1),[h,x]=c.useState(!0),[g,y]=c.useState("30d"),[p,j]=c.useState(!1);c.useEffect(()=>{(async()=>{try{const m=await k.load();m.appSettings&&t(m.appSettings)}catch(m){console.error("Failed to load settings:",m)}finally{x(!1)}})()},[]);const S=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:e.jsx(Q,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics",icon:e.jsx(Ce,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and answers",icon:e.jsx(Le,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"About SahAI Extension",icon:e.jsx(Y,{size:16,className:"text-adobe-accent"})}],v=(()=>{const d=Date.now(),m=n.filter(f=>{if(g==="all")return!0;const w=parseInt(g.replace("d",""));return d-f.createdAt<=w*24*60*60*1e3});return{messages:m.reduce((f,w)=>f+w.messages.length,0),sessions:m.length,tokens:m.reduce((f,w)=>f+(w.tokenCount||0),0),cost:m.reduce((f,w)=>f+(w.cost||0),0),avgLatency:m.length>0?m.reduce((f,w)=>f+(w.avgLatency||0),0)/m.length:0}})(),N=()=>{j(!0),setTimeout(()=>j(!1),800)},b=async()=>{l(!0);try{const m={...await k.load(),appSettings:r};await k.save(m),o()}catch(d){console.error("Failed to save settings:",d)}finally{l(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(O,{size:20})})]})}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:e.jsx("div",{className:"space-y-1",children:S.map(d=>e.jsx("button",{onClick:()=>a(d.id),className:`w-full text-left p-3 rounded-md transition-colors ${s===d.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:d.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:d.title}),e.jsx("div",{className:"text-xs mt-1",children:d.description})]})]})},d.id))})}),e.jsx("div",{className:"w-2/3 p-6 overflow-y-auto",children:h?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):s==="settings"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:r.theme,onChange:d=>t(m=>({...m,theme:d.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[e.jsx("option",{value:"auto",children:"Auto (System)"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"dark",children:"Dark"})]}),e.jsx(P,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:r.autoSave,onChange:d=>t(m=>({...m,autoSave:d.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:r.showNotifications,onChange:d=>t(m=>({...m,showNotifications:d.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",r.maxHistoryItems,")"]}),e.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:r.maxHistoryItems,onChange:d=>t(m=>({...m,maxHistoryItems:parseInt(d.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:r.debugMode,onChange:d=>t(m=>({...m,debugMode:d.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:b,disabled:i,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(Q,{size:16}),e.jsx("span",{children:i?"Saving...":"Save Settings"})]})})]}):s==="analytics"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Analytics"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:g,onChange:d=>y(d.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(P,{size:14,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),e.jsx("button",{onClick:N,disabled:p,className:"p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(re,{size:16,className:p?"animate-spin":""})})]})]}),p?e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(4)].map((d,m)=>e.jsx("div",{className:"h-24 bg-adobe-bg-secondary rounded-md animate-pulse"},m))}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Messages"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:v.messages.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Sessions"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:v.sessions.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Tokens Used"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:v.tokens.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Est. Cost"}),e.jsxs("div",{className:"text-2xl font-medium text-adobe-text-primary",children:["$",v.cost.toFixed(4)]})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Average Latency"}),e.jsxs("div",{className:"text-sm font-medium text-adobe-text-primary",children:[v.avgLatency.toFixed(2)," seconds"]})]}),e.jsx("div",{className:"h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-adobe-accent transition-all duration-300",style:{width:`${Math.min(100,v.avgLatency*50)}%`}})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary mb-2",children:"Performance Tips"}),e.jsxs("ul",{className:"text-sm text-adobe-text-secondary space-y-1",children:[e.jsx("li",{children:"• Use concise prompts to reduce token usage"}),e.jsx("li",{children:"• Select faster models for simple tasks"}),e.jsx("li",{children:"• Monitor usage to optimize costs"})]})]})]})]}):s==="help"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}):e.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Y,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),e.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),e.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center",children:[s==="settings"&&"General Settings",s==="analytics"&&"Usage Analytics",s==="help"&&"Help & Support",s==="about"&&"About SahAI"]})]})})},et=()=>{const{closeModal:o}=I(),{sessions:n,isLoading:r,error:t,loadHistory:s,deleteSession:a,setCurrentSession:i,getSortedSessions:l}=q(),[h,x]=c.useState(""),[g,y]=c.useState(null),[p,j]=c.useState("recent");c.useEffect(()=>{s()},[s]);const S=l().filter(b=>b.title.toLowerCase().includes(h.toLowerCase())||b.messages.some(d=>d.content.toLowerCase().includes(h.toLowerCase()))).sort((b,d)=>p==="alphabetical"?b.title.localeCompare(d.title):p==="oldest"?b.createdAt-d.createdAt:d.createdAt-b.createdAt),C=async(b,d)=>{d.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await a(b)},v=b=>{const d=new Date(b),f=(new Date().getTime()-d.getTime())/(1e3*60*60);return f<24?d.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):f<24*7?d.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):d.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},N=b=>{const d=b.messages[b.messages.length-1];if(!d)return"No messages";const m=d.content.slice(0,100);return m.length<d.content.length?`${m}...`:m};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(O,{size:20})})]})}),e.jsx("div",{className:"p-4 border-b border-adobe-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:h,onChange:b=>x(b.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),e.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>x(""),children:h?e.jsx(O,{size:16}):e.jsx(ke,{size:16})})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:p,onChange:b=>j(b.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"recent",children:"Most Recent"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),e.jsx(P,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:r?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):t?e.jsxs("div",{className:"p-4 text-center",children:[e.jsxs("div",{className:"bg-red-900/20 border border-red-800/50 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-red-400 font-medium mb-2",children:"Error loading history:"}),e.jsx("p",{className:"text-sm text-red-300",children:t})]}),e.jsx("button",{onClick:s,className:"px-4 py-2 bg-adobe-accent hover:bg-adobe-accent-hover text-white rounded-md transition-colors",children:"Retry Loading History"})]}):S.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(W,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:h?"No matching sessions found":"No chat history yet"}),e.jsx("p",{className:"text-sm",children:h?"Try a different search term":"Start a new conversation to see it here"})]}):e.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:S.map(b=>e.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${(g==null?void 0:g.id)===b.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>y(b),children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:b.title}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:N(b)})]}),e.jsx("button",{onClick:d=>C(b.id,d),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:e.jsx(Ee,{size:14})})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(W,{size:12}),e.jsxs("span",{children:[b.messages.length," messages"]})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(Me,{size:12}),e.jsx("span",{children:v(b.createdAt)})]})]})]},b.id))})}),e.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center",children:["Showing ",S.length," of ",n.length," chat sessions"]})]})})},tt=()=>{const{closeModal:o}=I(),{getActiveProvider:n,providers:r}=R(),[t,s]=c.useState({isOnline:null,isChecking:!1}),a=n(),i=async(y=!1)=>{if(!(a!=null&&a.isConfigured)){s({isOnline:null,isChecking:!1});return}s(p=>({...p,isChecking:!0,error:void 0}));try{const p=await U.checkProviderStatus(a.id,{apiKey:a.apiKey,baseURL:a.baseURL});s({isOnline:p.isOnline,latency:p.latency,isChecking:!1,lastChecked:Date.now()})}catch(p){s({isOnline:!1,isChecking:!1,error:p.message,lastChecked:Date.now()})}};c.useEffect(()=>{i();const y=setInterval(i,3e4);return()=>clearInterval(y)},[a]);const l=()=>t.isChecking?e.jsx(G,{size:20,className:"animate-spin text-yellow-500"}):t.isOnline===!0?e.jsx(Pe,{size:20,className:"text-green-500"}):t.isOnline===!1?e.jsx(Ie,{size:20,className:"text-red-500"}):e.jsx(X,{size:20,className:"text-gray-500"}),h=()=>t.isChecking?"Checking connection...":t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown",x=()=>t.isChecking?"text-yellow-600":t.isOnline===!0?"text-green-600":t.isOnline===!1?"text-red-600":"text-gray-600",g=()=>t.isOnline===!0?"good":t.isOnline===!1?"critical":"warning";return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Provider Status"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>i(!0),disabled:t.isChecking,className:"p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors disabled:opacity-50",title:"Refresh status",children:e.jsx(re,{size:18,className:t.isChecking?"animate-spin":""})}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(O,{size:20})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-4",children:a?e.jsxs("div",{className:"h-full flex flex-col gap-4",children:[e.jsx("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:l()}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary",children:a.name}),e.jsx("p",{className:`text-sm font-medium ${x()}`,children:h()})]})]}),e.jsx("div",{className:`text-xs px-2 py-1 rounded ${g()==="good"?"bg-green-900/30 text-green-500":g()==="warning"?"bg-yellow-900/30 text-yellow-500":"bg-red-900/30 text-red-500"}`,children:h()})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-adobe-text-secondary",children:"Latency"}),e.jsx("span",{className:"text-xs text-adobe-text-tertiary",children:"Lower is better"})]}),e.jsx("div",{className:"mt-2",children:t.latency?e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsxs("span",{className:"text-2xl font-medium text-adobe-text-primary",children:[t.latency,"ms"]}),e.jsx("span",{className:`text-xs mb-1 ${t.latency<100?"text-green-500":t.latency<300?"text-yellow-500":"text-red-500"}`,children:t.latency<100?"Excellent":t.latency<300?"Good":"Poor"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Last Checked"}),e.jsx("div",{className:"mt-2",children:t.lastChecked?e.jsxs("div",{className:"text-adobe-text-primary",children:[e.jsx("div",{className:"text-xl font-medium",children:new Date(t.lastChecked).toLocaleTimeString()}),e.jsx("div",{className:"text-xs text-adobe-text-tertiary mt-1",children:new Date(t.lastChecked).toLocaleDateString()})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border col-span-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Endpoint"}),e.jsx("div",{className:"mt-2",children:a.baseURL?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"truncate text-adobe-text-primary font-mono text-sm",children:a.baseURL}),e.jsx("button",{className:"text-xs text-adobe-accent hover:text-adobe-accent-hover",onClick:()=>navigator.clipboard.writeText(a.baseURL||""),children:"Copy"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"Not configured"})})]})]}),e.jsxs("div",{className:"flex-1 bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border overflow-auto",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-2",children:"Status Details"}),t.error?e.jsxs("div",{className:"p-3 bg-red-900/20 border border-red-800/50 rounded text-sm text-red-400",children:[e.jsx("div",{className:"font-medium mb-1",children:"Error:"}),e.jsx("div",{children:t.error})]}):e.jsx("div",{className:"text-sm text-adobe-text-primary",children:t.isChecking?"Checking provider status...":t.isOnline===!0?"Provider is online and responding normally.":t.isOnline===!1?"Provider is offline or not responding to requests.":"Provider status unknown. Please check configuration."})]})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(X,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"No provider selected"}),e.jsx("p",{className:"text-sm",children:"Select a provider to check connection status"})]})}),e.jsx("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center bg-adobe-bg-secondary",children:"Status checks are performed automatically every 30 seconds"})]})})},st=()=>{const{modal:o}=I();if(!o)return null;switch(o){case"provider":return e.jsx(Xe,{});case"settings":return e.jsx(Ze,{});case"chat-history":return e.jsx(et,{});case"status":return e.jsx(tt,{});default:return null}},rt=()=>e.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[e.jsx(Fe,{}),e.jsx(Qe,{}),e.jsx(Ye,{}),e.jsx(st,{})]});ne();R.getState().loadSettings();H.createRoot(document.getElementById("root")).render(e.jsx(se.StrictMode,{children:e.jsx(rt,{})}));
