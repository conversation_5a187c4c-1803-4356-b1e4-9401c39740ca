<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <title>SahAI V2 Chat</title>
  <!-- CEP JavaScript API - must be loaded before any CEP functionality -->
  <script src="./CSInterface.js" defer></script>
  <script type="module" crossorigin src="./index.js"></script>
  <link rel="modulepreload" crossorigin href="./assets/react-vendor-CIP6LD3P.js">
  <link rel="modulepreload" crossorigin href="./assets/shiki-DBOBms81.js">
  <link rel="modulepreload" crossorigin href="./assets/ui-vendor-CWg08DdH.js">
  <link rel="stylesheet" crossorigin href="./assets/index-DDyLUOr6.css">
</head>
<body class="bg-adobe-bg-primary text-adobe-text-primary font-sans">
  <div id="root"></div>
</body>
</html>
